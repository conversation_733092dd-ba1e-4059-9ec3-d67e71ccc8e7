<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Mon Sep 01 08:08:26 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:08:26,458</td>
<td class="Message">null Unexpected exception during request: java.net.SocketException: Connection reset</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">217</td>
</tr>
<tr><td class="Exception" colspan="6">java.net.SocketException: Connection reset
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:08:26,458</td>
<td class="Message">null Unexpected exception during request: java.net.SocketException: Connection reset</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">217</td>
</tr>
<tr><td class="Exception" colspan="6">java.net.SocketException: Connection reset
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:08:26,458</td>
<td class="Message">null Unexpected exception during request: java.net.SocketException: Connection reset</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">217</td>
</tr>
<tr><td class="Exception" colspan="6">java.net.SocketException: Connection reset
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:08:26,637</td>
<td class="Message">Reconnecting, last destination was /127.0.0.1:6666</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:08:26,637</td>
<td class="Message">Reconnecting, last destination was /127.0.0.1:6666</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:08:26,637</td>
<td class="Message">Reconnecting, last destination was /127.0.0.1:6666</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:08:26,668</td>
<td class="Message">Cannot reconnect to [127.0.0.1/&lt;unresolved&gt;:6666]: Connection refused: no further information: /127.0.0.1:6666</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:08:26,668</td>
<td class="Message">Cannot reconnect to [127.0.0.1/&lt;unresolved&gt;:6666]: Connection refused: no further information: /127.0.0.1:6666</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:08:26,668</td>
<td class="Message">Cannot reconnect to [127.0.0.1/&lt;unresolved&gt;:6666]: Connection refused: no further information: /127.0.0.1:6666</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Mon Sep 01 08:18:31 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:31,958</td>
<td class="Message">HV000001: Hibernate Validator 8.0.2.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:32,015</td>
<td class="Message">Starting JeecgSystemApplication using Java 17.0.15 with PID 30412 (D:\wd\3.8.1\boot\jeecg-module-system\jeecg-system-start\target\classes started by Administrator in D:\wd\3.8.1\boot)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:32,016</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">658</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:33,791</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">295</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:33,793</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">143</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:33,889</td>
<td class="Message">Finished Spring Data repository scanning in 82 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">211</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:33,988</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:33,989</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:33,989</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,168</td>
<td class="Message">Overriding bean definition for bean &#39;classifier&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,169</td>
<td class="Message">Overriding bean definition for bean &#39;end&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.b; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/b.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,170</td>
<td class="Message">Overriding bean definition for bean &#39;http&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.c; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/c.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,170</td>
<td class="Message">Overriding bean definition for bean &#39;knowledge&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.d; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/d.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,170</td>
<td class="Message">Overriding bean definition for bean &#39;llm&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.e; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/e.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,172</td>
<td class="Message">Overriding bean definition for bean &#39;enhanceJava&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.enhance.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/enhance/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,172</td>
<td class="Message">Overriding bean definition for bean &#39;reply&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.f; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/f.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,173</td>
<td class="Message">Overriding bean definition for bean &#39;start&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.g; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/g.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,173</td>
<td class="Message">Overriding bean definition for bean &#39;subflow&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.h; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/h.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,174</td>
<td class="Message">Overriding bean definition for bean &#39;switch&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.i; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/i.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,568</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportCategoryDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,569</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportJobDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportLogDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.JimuDragCategoryDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.JimuDragMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.JimuReportIconLibDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,570</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,571</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,571</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,571</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,571</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,571</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:34,571</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:34,573</td>
<td class="Message">Cannot enhance @Configuration bean definition &#39;com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration&#39; since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as &#39;static&#39; and/or marking the containing configuration class as &#39;proxyBeanMethods=false&#39;.</td>
<td class="MethodOfCaller">enhanceConfigurationClasses</td>
<td class="FileOfCaller">ConfigurationClassPostProcessor.java</td>
<td class="LineOfCaller">514</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,349</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,350</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">314</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,668</td>
<td class="Message">Tomcat initialized with port 9092 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,679</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-9092&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,680</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,680</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/10.1.40]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,737</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,738</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3507 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">301</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,811</td>
<td class="Message">Configuring camunda rest api.</td>
<td class="MethodOfCaller">registerCamundaRestResources</td>
<td class="FileOfCaller">CamundaJerseyResourceConfig.java</td>
<td class="LineOfCaller">38</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,828</td>
<td class="Message">Finished configuring camunda rest api.</td>
<td class="MethodOfCaller">registerCamundaRestResources</td>
<td class="FileOfCaller">CamundaJerseyResourceConfig.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,945</td>
<td class="Message"> Init JimuReport Config [ Token Interceptor &amp; Resource Locations ] </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:35,951</td>
<td class="Message">
================================================================================================
            _ _ __  __       ____                       _   
           | (_)  \/  |_   _|  _ \ ___ _ __   ___  _ __| |_ 
        _  | | | |\/| | | | | |_) / _ \ &#39;_ \ / _ \| &#39;__| __|
       | |_| | | |  | | |_| |  _ &lt;  __/ |_) | (_) | |  | |_ 
        \___/|_|_|  |_|\__,_|_| \_\___| .__/ \___/|_|   \__|
                                      |_|                   
		Version: 2.0.0
		打造 “简单|智能|专业 ” 的数据可视化报表。
		官网：https://jimureport.com
================================================================================================
</td>
<td class="MethodOfCaller">print</td>
<td class="FileOfCaller">LogoPrinter.java</td>
<td class="LineOfCaller">34</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:36,464</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">673</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:36,465</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:36,465</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:37,827</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,066</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,068</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,075</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,075</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,077</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,078</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,078</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-IGMMD5L1756685919067&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,079</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,079</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:39,079</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@42bd5540</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:40,830</td>
<td class="Message">component aspect implement[nodeProcessAspect] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">CmpAroundAspectBeanProcess.java</td>
<td class="LineOfCaller">32</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,039</td>
<td class="Message">proxy component[classifier] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,046</td>
<td class="Message">proxy component[end] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,053</td>
<td class="Message">proxy component[http] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,057</td>
<td class="Message">proxy component[knowledge] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,062</td>
<td class="Message">proxy component[llm] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,065</td>
<td class="Message">proxy component[enhanceJava] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,069</td>
<td class="Message">proxy component[reply] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,072</td>
<td class="Message">proxy component[start] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,076</td>
<td class="Message">proxy component[subflow] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,080</td>
<td class="Message">proxy component[switch] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,667</td>
<td class="Message"> Init JimuReport Config [ 线程池 ] </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:41,765</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">JmDragRedisConfig.java</td>
<td class="LineOfCaller">31</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,151</td>
<td class="Message">STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,299</td>
<td class="Message">ENGINE-12003 Plugin &#39;CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, createAdminUserConfiguration, failedJobConfiguration, CreateFilterConfiguration[filterName=All tasks], org.jeecg.config.CamundaConfiguration$1@516a9ded, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin, SpringBootSpinProcessEnginePlugin]&#39; activated on process engine &#39;default&#39;</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,306</td>
<td class="Message">EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,306</td>
<td class="Message">EVENTING-003: Task events will be published as Spring Events.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,306</td>
<td class="Message">EVENTING-005: Execution events will be published as Spring Events.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,306</td>
<td class="Message">EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,311</td>
<td class="Message">EVENTING-007: History events will be published as Spring events.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">78</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,316</td>
<td class="Message">SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormatProvider[name = application/json]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,558</td>
<td class="Message">SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormatProvider[name = application/xml]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,732</td>
<td class="Message">SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormat[name = application/xml]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,733</td>
<td class="Message">SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormat[name = application/json]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:42,944</td>
<td class="Message">FEEL/SCALA-01001 Spin value mapper detected</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:43,028</td>
<td class="Message">ENGINE-12020 The transaction isolation level set for the database is &#39;REPEATABLE_READ&#39; which differs from the recommended value and property skipIsolationLevelCheck is enabled. Please keep in mind that levels different from &#39;READ_COMMITTED&#39; are known to cause deadlocks and other unexpected behaviours.</td>
<td class="MethodOfCaller">logWarn</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:44,101</td>
<td class="Message">ENGINE-00001 Process Engine default created.</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:44,161</td>
<td class="Message">STARTER-SB016 Skip initial filter creation, the filter with this name already exists: All tasks</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:44,709</td>
<td class="Message">Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(jakarta.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final, consider using interface-based JDK proxies instead.</td>
<td class="MethodOfCaller">doValidateClass</td>
<td class="FileOfCaller">CglibAopProxy.java</td>
<td class="LineOfCaller">296</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:44,709</td>
<td class="Message">Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(jakarta.servlet.ServletContext)] because it is marked as final, consider using interface-based JDK proxies instead.</td>
<td class="MethodOfCaller">doValidateClass</td>
<td class="FileOfCaller">CglibAopProxy.java</td>
<td class="LineOfCaller">296</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:44,710</td>
<td class="Message">Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final, consider using interface-based JDK proxies instead.</td>
<td class="MethodOfCaller">doValidateClass</td>
<td class="FileOfCaller">CglibAopProxy.java</td>
<td class="LineOfCaller">296</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:44,937</td>
<td class="Message">Init Token ignoreAuthUrls Config [ 集合 ]  ：[/airag/flow/run, /airag/flow/run/*, /test/jeecgDemo/html, /openapi/demo/index, /airag/app/queryById, /airag/chat/stop/*, /airag/chat/send, /airag/chat/upload, /airag/chat/conversation/update/title, /airag/chat/conversation/*, /airag/chat/init, /airag/chat/conversations, /airag/chat/messages/clear/*, /airag/chat/messages]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">IgnoreAuthPostProcessor.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:44,938</td>
<td class="Message">Init Token ignoreAuthUrls Config [ 耗时 ] ：4毫秒</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">IgnoreAuthPostProcessor.java</td>
<td class="LineOfCaller">49</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:44,950</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:45,402</td>
<td class="Message">Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@77128dab</td>
<td class="MethodOfCaller">setApplicationContext</td>
<td class="FileOfCaller">MybatisPlusApplicationContextAware.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:45,443</td>
<td class="Message">lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazySecurityFilter@a538a7f</td>
<td class="MethodOfCaller">lazyInit</td>
<td class="FileOfCaller">LazyInitRegistration.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:45,444</td>
<td class="Message">lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazyProcessEnginesFilter@81ba0c5</td>
<td class="MethodOfCaller">lazyInit</td>
<td class="FileOfCaller">LazyInitRegistration.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,163</td>
<td class="Message">未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件</td>
<td class="MethodOfCaller">magicNotifyService</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,202</td>
<td class="Message">注册数据源：default</td>
<td class="MethodOfCaller">put</td>
<td class="FileOfCaller">MagicDynamicDataSource.java</td>
<td class="LineOfCaller">67</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,204</td>
<td class="Message">未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)</td>
<td class="MethodOfCaller">pageProvider</td>
<td class="FileOfCaller">MagicModuleConfiguration.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,205</td>
<td class="Message">未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)</td>
<td class="MethodOfCaller">sqlCache</td>
<td class="FileOfCaller">MagicModuleConfiguration.java</td>
<td class="LineOfCaller">122</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,288</td>
<td class="Message">magic-api工作目录:db://api_file/magic-api</td>
<td class="MethodOfCaller">magicConfiguration</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">302</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,293</td>
<td class="Message">注册模块:log -&gt; interface org.slf4j.Logger</td>
<td class="MethodOfCaller">setupMagicModules</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">267</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,298</td>
<td class="Message">注册模块:db -&gt; class org.ssssssss.magicapi.modules.db.SQLModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,298</td>
<td class="Message">注册模块:http -&gt; class org.ssssssss.magicapi.modules.http.HttpModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,298</td>
<td class="Message">注册模块:env -&gt; class org.ssssssss.magicapi.modules.spring.EnvModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,298</td>
<td class="Message">注册模块:request -&gt; class org.ssssssss.magicapi.modules.servlet.RequestModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,298</td>
<td class="Message">注册模块:response -&gt; class org.ssssssss.magicapi.modules.servlet.ResponseModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,299</td>
<td class="Message">注册模块:magic -&gt; class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,299</td>
<td class="Message">自动导入模块：db</td>
<td class="MethodOfCaller">lambda$setupMagicModules$8</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">280</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,301</td>
<td class="Message">注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -&gt; class org.ssssssss.magicapi.servlet.jakarta.MagicJakartaResponseExtension</td>
<td class="MethodOfCaller">lambda$setupMagicModules$10</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">288</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,325</td>
<td class="Message">Exposing 3 endpoints beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">60</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,478</td>
<td class="Message">flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser</td>
<td class="MethodOfCaller">info</td>
<td class="FileOfCaller">LFLog.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,501</td>
<td class="Message">query sql: select id, application_name, chain from airag_flow where status = &#39;enable&#39; and chain is not null</td>
<td class="MethodOfCaller">info</td>
<td class="FileOfCaller">LFLog.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,505</td>
<td class="Message">use dataSourceName[dataSource],has found liteflow config</td>
<td class="MethodOfCaller">getConn</td>
<td class="FileOfCaller">LiteFlowJdbcUtil.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,727</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-9092&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:46,734</td>
<td class="Message">Tomcat started on port 9092 (http) with context path &#39;/tld_ai&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">243</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,092</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">735</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,107</td>
<td class="Message">Started JeecgSystemApplication in 15.59 seconds (process running for 30.866)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,180</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">30</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,201</td>
<td class="Message"> Init Code Generate Template completed in 21 ms</td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">33</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:47,204</td>
<td class="Message">A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.</td>
<td class="MethodOfCaller">logWarningAboutLateFilter</td>
<td class="FileOfCaller">MeterRegistry.java</td>
<td class="LineOfCaller">881</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:47,204</td>
<td class="Message">A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.</td>
<td class="MethodOfCaller">logWarningAboutLateFilter</td>
<td class="FileOfCaller">MeterRegistry.java</td>
<td class="LineOfCaller">881</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-01 08:18:47,204</td>
<td class="Message">A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.</td>
<td class="MethodOfCaller">logWarningAboutLateFilter</td>
<td class="FileOfCaller">MeterRegistry.java</td>
<td class="LineOfCaller">881</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,219</td>
<td class="Message">ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,220</td>
<td class="Message">ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:47,222</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:9092/tld_ai/doc.html
	External: 	http://**************:9092/tld_ai/doc.html
	Swagger文档: 	http://**************:9092/tld_ai/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:48,097</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">lambda$startScheduler$0</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">749</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:48,111</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:48,111</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-IGMMD5L1756438449438&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:18:48,145</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-IGMMD5L1756685919067 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:22,559</td>
<td class="Message">A cookie header was received [Hm_lvt_606a102b298cb00317d5a96037729e23=1754894829,1754908301,1755044903;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:22,567</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:22,567</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">532</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:22,570</td>
<td class="Message">Completed initialization in 3 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">554</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:22,745</td>
<td class="Message">加密操作，Aspect程序耗时：10ms</td>
<td class="MethodOfCaller">around</td>
<td class="FileOfCaller">SensitiveDataAspect.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:25,443</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:25,457</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)23毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:26:25,462</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)28毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:26,012</td>
<td class="Message">======获取全部菜单数据=====耗时:31毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">114</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:32,991</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------useId： 1945389941002952705,Roles size: 1</td>
<td class="MethodOfCaller">getUserRoleSetById</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1131</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:33,005</td>
<td class="Message">-------通过数据库读取用户拥有的权限Perms------userId： 1945389941002952705,Perms size: 141</td>
<td class="MethodOfCaller">getUserPermissionSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1162</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:33,005</td>
<td class="Message">===============Shiro权限认证成功==============</td>
<td class="MethodOfCaller">doGetAuthorizationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:55,444</td>
<td class="Message">==========接收到请求:[/drag/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:55,968</td>
<td class="Message">==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:55,973</td>
<td class="Message">==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:55,978</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:55,988</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1</td>
<td class="MethodOfCaller">getUserRoleSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1117</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,020</td>
<td class="Message">==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,038</td>
<td class="Message">---------自定义页面列表-------req.getParameter.lowAPPId:</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,038</td>
<td class="Message">---------自定义页面列表-------onlDragPage.getLowAppId:null</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,038</td>
<td class="Message">进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,140</td>
<td class="Message">==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,145</td>
<td class="Message">---------自定义页面列表-------req.getParameter.lowAPPId:</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,145</td>
<td class="Message">---------自定义页面列表-------onlDragPage.getLowAppId:null</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:56,146</td>
<td class="Message">进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:57,911</td>
<td class="Message">==========接收到请求:[/drag/index],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:57,915</td>
<td class="Message"> index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">DragIndexController.java</td>
<td class="LineOfCaller">81</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:58,084</td>
<td class="Message">==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:58,088</td>
<td class="Message">==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:58,154</td>
<td class="Message">查询仪表盘信息，仪表盘名字：车间生产管理，ID:1060099867109019648</td>
<td class="MethodOfCaller">d</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">316</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:58,282</td>
<td class="Message">==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:58,282</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:29:58,292</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1</td>
<td class="MethodOfCaller">getUserRoleSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1117</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:04,595</td>
<td class="Message">==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:04,598</td>
<td class="Message">---------自定义页面列表-------req.getParameter.lowAPPId:</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:04,598</td>
<td class="Message">---------自定义页面列表-------onlDragPage.getLowAppId:null</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:04,598</td>
<td class="Message">进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:06,495</td>
<td class="Message">==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:06,498</td>
<td class="Message">---------自定义页面列表-------req.getParameter.lowAPPId:</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">73</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:06,498</td>
<td class="Message">---------自定义页面列表-------onlDragPage.getLowAppId:null</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:06,499</td>
<td class="Message">进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,293</td>
<td class="Message">==========接收到请求:[/drag/index],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,296</td>
<td class="Message"> index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">DragIndexController.java</td>
<td class="LineOfCaller">81</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,463</td>
<td class="Message">==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,466</td>
<td class="Message">==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,468</td>
<td class="Message">查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:965205492447150080</td>
<td class="MethodOfCaller">d</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">316</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,568</td>
<td class="Message">==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,568</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,574</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1</td>
<td class="MethodOfCaller">getUserRoleSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1117</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,721</td>
<td class="Message">==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:09,721</td>
<td class="Message">==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:22,893</td>
<td class="Message">==========接收到请求:[/drag/index],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:22,897</td>
<td class="Message"> index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">DragIndexController.java</td>
<td class="LineOfCaller">81</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:22,960</td>
<td class="Message">==========接收到请求:[/drag/page/addVisitsNumber],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:23,049</td>
<td class="Message">==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:23,049</td>
<td class="Message">==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,020</td>
<td class="Message">==========接收到请求:[/drag/index],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,023</td>
<td class="Message"> index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">DragIndexController.java</td>
<td class="LineOfCaller">81</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,104</td>
<td class="Message">==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,106</td>
<td class="Message">==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,108</td>
<td class="Message">查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:1060068138562408448</td>
<td class="MethodOfCaller">d</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">316</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,179</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,179</td>
<td class="Message">==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,184</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1</td>
<td class="MethodOfCaller">getUserRoleSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,232</td>
<td class="Message">==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:40,232</td>
<td class="Message">==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,658</td>
<td class="Message">==========接收到请求:[/drag/index],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,661</td>
<td class="Message"> index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4</td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">DragIndexController.java</td>
<td class="LineOfCaller">81</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,742</td>
<td class="Message">==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,745</td>
<td class="Message">==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,749</td>
<td class="Message">查询仪表盘信息，仪表盘名字：物业消防巡检状态，ID:993809146154418176</td>
<td class="MethodOfCaller">d</td>
<td class="FileOfCaller">OnlDragPageController.java</td>
<td class="LineOfCaller">316</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,819</td>
<td class="Message">==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,819</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,824</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1</td>
<td class="MethodOfCaller">getUserRoleSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:43,925</td>
<td class="Message">==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:44,536</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getMapDataByCode],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:59,184</td>
<td class="Message">==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-01 08:30:59,290</td>
<td class="Message">==========接收到请求:[/drag/onlDragDatasetHead/getMapDataByCode],ip:[127.0.0.1]==========</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">JimuReportTokenInterceptor.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-09-01 08:31:24,600</td>
<td class="Message">{conn-10004, pstmt-20065} execute error. INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)</td>
<td class="MethodOfCaller">statementLogError</td>
<td class="FileOfCaller">Slf4jLogFilter.java</td>
<td class="LineOfCaller">157</td>
</tr>
<tr><td class="Exception" colspan="6">com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-09-01 08:31:24,727</td>
<td class="Message">
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
; Data truncation: Data too long for column &#39;url&#39; at row 1</td>
<td class="MethodOfCaller">handleDataIntegrityViolationException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">193</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
; Data truncation: Data too long for column &#39;url&#39; at row 1
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
<br />Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 139 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-09-01 08:31:39,850</td>
<td class="Message">{conn-10004, pstmt-20066} execute error. INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)</td>
<td class="MethodOfCaller">statementLogError</td>
<td class="FileOfCaller">Slf4jLogFilter.java</td>
<td class="LineOfCaller">157</td>
</tr>
<tr><td class="Exception" colspan="6">com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-09-01 08:31:39,854</td>
<td class="Message">
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
; Data truncation: Data too long for column &#39;url&#39; at row 1</td>
<td class="MethodOfCaller">handleDataIntegrityViolationException</td>
<td class="FileOfCaller">JeecgBootExceptionHandler.java</td>
<td class="LineOfCaller">193</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
; Data truncation: Data too long for column &#39;url&#39; at row 1
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:842)
<br />Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column &#39;url&#39; at row 1
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 139 common frames omitted
</td></tr>