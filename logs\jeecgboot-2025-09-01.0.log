2025-09-01 08:08:26.458 [lettuce-nioEventLoop-4-1] INFO  io.lettuce.core.protocol.CommandHandler:217 - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 08:08:26.458 [lettuce-nioEventLoop-4-2] INFO  io.lettuce.core.protocol.CommandHandler:217 - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 08:08:26.458 [lettuce-nioEventLoop-4-3] INFO  io.lettuce.core.protocol.CommandHandler:217 - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 08:08:26.637 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was /127.0.0.1:6666
2025-09-01 08:08:26.637 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was /127.0.0.1:6666
2025-09-01 08:08:26.637 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was /127.0.0.1:6666
2025-09-01 08:08:26.668 [lettuce-nioEventLoop-4-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [127.0.0.1/<unresolved>:6666]: Connection refused: no further information: /127.0.0.1:6666
2025-09-01 08:08:26.668 [lettuce-nioEventLoop-4-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [127.0.0.1/<unresolved>:6666]: Connection refused: no further information: /127.0.0.1:6666
2025-09-01 08:08:26.668 [lettuce-nioEventLoop-4-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [127.0.0.1/<unresolved>:6666]: Connection refused: no further information: /127.0.0.1:6666
2025-09-01 08:18:31.958 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 8.0.2.Final
2025-09-01 08:18:32.015 [main] INFO  org.jeecg.JeecgSystemApplication:53 - Starting JeecgSystemApplication using Java 17.0.15 with PID 30412 (D:\wd\3.8.1\boot\jeecg-module-system\jeecg-system-start\target\classes started by Administrator in D:\wd\3.8.1\boot)
2025-09-01 08:18:32.016 [main] INFO  org.jeecg.JeecgSystemApplication:658 - The following 1 profile is active: "dev"
2025-09-01 08:18:33.791 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:295 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-01 08:18:33.793 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:143 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-01 08:18:33.889 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:211 - Finished Spring Data repository scanning in 82 ms. Found 0 Redis repository interfaces.
2025-09-01 08:18:33.988 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-09-01 08:18:33.989 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
2025-09-01 08:18:33.989 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-09-01 08:18:34.168 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'classifier' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.169 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'end' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.b; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/b.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.170 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'http' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.c; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/c.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.170 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'knowledge' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.d; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/d.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.170 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'llm' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.e; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/e.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.172 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'enhanceJava' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.enhance.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/enhance/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.172 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'reply' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.f; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/f.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.173 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'start' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.g; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/g.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.173 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'subflow' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.h; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/h.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.174 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'switch' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.i; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/i.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-01 08:18:34.568 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportCategoryDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-09-01 08:18:34.569 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportJobDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportLogDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.JimuDragCategoryDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.JimuDragMapDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.JimuReportIconLibDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }
2025-09-01 08:18:34.570 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }
2025-09-01 08:18:34.571 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }
2025-09-01 08:18:34.571 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }
2025-09-01 08:18:34.571 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }
2025-09-01 08:18:34.571 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }
2025-09-01 08:18:34.571 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }
2025-09-01 08:18:34.571 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragShareDao }
2025-09-01 08:18:34.573 [main] WARN  o.s.c.annotation.ConfigurationClassPostProcessor:514 - Cannot enhance @Configuration bean definition 'com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-09-01 08:18:35.349 [main] INFO  org.jeecg.config.shiro.ShiroConfig:293 - ===============(1)创建缓存管理器RedisCacheManager
2025-09-01 08:18:35.350 [main] INFO  org.jeecg.config.shiro.ShiroConfig:314 - ===============(2)创建RedisManager,连接Redis..
2025-09-01 08:18:35.668 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:111 - Tomcat initialized with port 9092 (http)
2025-09-01 08:18:35.679 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-9092"]
2025-09-01 08:18:35.680 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-09-01 08:18:35.680 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-09-01 08:18:35.737 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/tld_ai]:168 - Initializing Spring embedded WebApplicationContext
2025-09-01 08:18:35.738 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 3507 ms
2025-09-01 08:18:35.811 [main] INFO  o.c.b.s.b.starter.rest.CamundaJerseyResourceConfig:38 - Configuring camunda rest api.
2025-09-01 08:18:35.828 [main] INFO  o.c.b.s.b.starter.rest.CamundaJerseyResourceConfig:44 - Finished configuring camunda rest api.
2025-09-01 08:18:35.945 [main] INFO  o.j.m.jmreport.config.init.JimuReportConfiguration:130 -  Init JimuReport Config [ Token Interceptor & Resource Locations ] 
2025-09-01 08:18:35.951 [main] INFO  o.jeecg.modules.jmreport.config.init.LogoPrinter:34 - 
================================================================================================
            _ _ __  __       ____                       _   
           | (_)  \/  |_   _|  _ \ ___ _ __   ___  _ __| |_ 
        _  | | | |\/| | | | | |_) / _ \ '_ \ / _ \| '__| __|
       | |_| | | |  | | |_| |  _ <  __/ |_) | (_) | |  | |_ 
        \___/|_|_|  |_|\__,_|_| \_\___| .__/ \___/|_|   \__|
                                      |_|                   
		Version: 2.0.0
		打造 “简单|智能|专业 ” 的数据可视化报表。
		官网：https://jimureport.com
================================================================================================

2025-09-01 08:18:36.464 [main] INFO  com.alibaba.druid.pool.DruidDataSource:673 - {dataSource-1,master} inited
2025-09-01 08:18:36.465 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-09-01 08:18:36.465 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-01 08:18:37.827 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:53 -  --- redis config init --- 
2025-09-01 08:18:39.066 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-09-01 08:18:39.068 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-09-01 08:18:39.075 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-09-01 08:18:39.075 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-09-01 08:18:39.077 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-09-01 08:18:39.078 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-09-01 08:18:39.078 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-IGMMD5L1756685919067'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-09-01 08:18:39.079 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-09-01 08:18:39.079 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-09-01 08:18:39.079 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@42bd5540
2025-09-01 08:18:40.830 [main] INFO  c.y.l.process.impl.CmpAroundAspectBeanProcess:32 - component aspect implement[nodeProcessAspect] has been found
2025-09-01 08:18:41.039 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[classifier] has been found
2025-09-01 08:18:41.046 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[end] has been found
2025-09-01 08:18:41.053 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[http] has been found
2025-09-01 08:18:41.057 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[knowledge] has been found
2025-09-01 08:18:41.062 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[llm] has been found
2025-09-01 08:18:41.065 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[enhanceJava] has been found
2025-09-01 08:18:41.069 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[reply] has been found
2025-09-01 08:18:41.072 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[start] has been found
2025-09-01 08:18:41.076 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[subflow] has been found
2025-09-01 08:18:41.080 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[switch] has been found
2025-09-01 08:18:41.667 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:42 -  Init JimuReport Config [ 线程池 ] 
2025-09-01 08:18:41.765 [main] INFO  org.jeecg.modules.drag.config.a.b.a:31 -  --- redis config init --- 
2025-09-01 08:18:42.151 [main] INFO  org.camunda.bpm.spring.boot:187 - STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10
2025-09-01 08:18:42.299 [main] INFO  org.camunda.bpm.engine.cfg:187 - ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, createAdminUserConfiguration, failedJobConfiguration, CreateFilterConfiguration[filterName=All tasks], org.jeecg.config.CamundaConfiguration$1@516a9ded, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin, SpringBootSpinProcessEnginePlugin]' activated on process engine 'default'
2025-09-01 08:18:42.306 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:56 - EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-09-01 08:18:42.306 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:58 - EVENTING-003: Task events will be published as Spring Events.
2025-09-01 08:18:42.306 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:64 - EVENTING-005: Execution events will be published as Spring Events.
2025-09-01 08:18:42.306 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:69 - EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-09-01 08:18:42.311 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:78 - EVENTING-007: History events will be published as Spring events.
2025-09-01 08:18:42.316 [main] INFO  org.camunda.spin:187 - SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormatProvider[name = application/json]
2025-09-01 08:18:42.558 [main] INFO  org.camunda.spin:187 - SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormatProvider[name = application/xml]
2025-09-01 08:18:42.732 [main] INFO  org.camunda.spin:187 - SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormat[name = application/xml]
2025-09-01 08:18:42.733 [main] INFO  org.camunda.spin:187 - SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormat[name = application/json]
2025-09-01 08:18:42.944 [main] INFO  org.camunda.bpm.dmn.feel.scala:187 - FEEL/SCALA-01001 Spin value mapper detected
2025-09-01 08:18:43.028 [main] WARN  org.camunda.bpm.engine.cfg:201 - ENGINE-12020 The transaction isolation level set for the database is 'REPEATABLE_READ' which differs from the recommended value and property skipIsolationLevelCheck is enabled. Please keep in mind that levels different from 'READ_COMMITTED' are known to cause deadlocks and other unexpected behaviours.
2025-09-01 08:18:44.101 [main] INFO  org.camunda.bpm.engine:187 - ENGINE-00001 Process Engine default created.
2025-09-01 08:18:44.161 [main] INFO  org.camunda.bpm.spring.boot:187 - STARTER-SB016 Skip initial filter creation, the filter with this name already exists: All tasks
2025-09-01 08:18:44.709 [main] WARN  org.springframework.aop.framework.CglibAopProxy:296 - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(jakarta.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-01 08:18:44.709 [main] WARN  org.springframework.aop.framework.CglibAopProxy:296 - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(jakarta.servlet.ServletContext)] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-01 08:18:44.710 [main] WARN  org.springframework.aop.framework.CglibAopProxy:296 - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-01 08:18:44.937 [main] INFO  o.j.config.shiro.ignore.IgnoreAuthPostProcessor:41 - Init Token ignoreAuthUrls Config [ 集合 ]  ：[/airag/flow/run, /airag/flow/run/*, /test/jeecgDemo/html, /openapi/demo/index, /airag/app/queryById, /airag/chat/stop/*, /airag/chat/send, /airag/chat/upload, /airag/chat/conversation/update/title, /airag/chat/conversation/*, /airag/chat/init, /airag/chat/conversations, /airag/chat/messages/clear/*, /airag/chat/messages]
2025-09-01 08:18:44.938 [main] INFO  o.j.config.shiro.ignore.IgnoreAuthPostProcessor:49 - Init Token ignoreAuthUrls Config [ 耗时 ] ：4毫秒
2025-09-01 08:18:44.950 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:50 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-09-01 08:18:45.402 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware:40 - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@77128dab
2025-09-01 08:18:45.443 [main] INFO  o.c.b.s.b.s.webapp.filter.LazyInitRegistration:66 - lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazySecurityFilter@a538a7f
2025-09-01 08:18:45.444 [main] INFO  o.c.b.s.b.s.webapp.filter.LazyInitRegistration:66 - lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazyProcessEnginesFilter@81ba0c5
2025-09-01 08:18:46.163 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:221 - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-09-01 08:18:46.202 [main] INFO  o.s.m.datasource.model.MagicDynamicDataSource:67 - 注册数据源：default
2025-09-01 08:18:46.204 [main] INFO  o.s.m.spring.boot.starter.MagicModuleConfiguration:111 - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
2025-09-01 08:18:46.205 [main] INFO  o.s.m.spring.boot.starter.MagicModuleConfiguration:122 - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-09-01 08:18:46.288 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:302 - magic-api工作目录:db://api_file/magic-api
2025-09-01 08:18:46.293 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:267 - 注册模块:log -> interface org.slf4j.Logger
2025-09-01 08:18:46.298 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-09-01 08:18:46.298 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-09-01 08:18:46.298 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-09-01 08:18:46.298 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-09-01 08:18:46.298 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-09-01 08:18:46.299 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-09-01 08:18:46.299 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:280 - 自动导入模块：db
2025-09-01 08:18:46.301 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:288 - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.jakarta.MagicJakartaResponseExtension
2025-09-01 08:18:46.325 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:60 - Exposing 3 endpoints beneath base path '/actuator'
2025-09-01 08:18:46.478 [main] INFO  c.y.liteflow.parser.factory.FlowParserProvider:193 - flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser
2025-09-01 08:18:46.501 [main] INFO  c.yomahub.liteflow.parser.sql.read.AbstractSqlRead:186 - query sql: select id, application_name, chain from airag_flow where status = 'enable' and chain is not null
2025-09-01 08:18:46.505 [main] INFO  c.y.liteflow.parser.sql.util.LiteFlowJdbcUtil:43 - use dataSourceName[dataSource],has found liteflow config
2025-09-01 08:18:46.727 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-9092"]
2025-09-01 08:18:46.734 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:243 - Tomcat started on port 9092 (http) with context path '/tld_ai'
2025-09-01 08:18:47.092 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:735 - Will start Quartz Scheduler [MyScheduler] in 1 seconds
2025-09-01 08:18:47.107 [main] INFO  org.jeecg.JeecgSystemApplication:59 - Started JeecgSystemApplication in 15.59 seconds (process running for 30.866)
2025-09-01 08:18:47.180 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:30 -  Init Code Generate Template [ 检测如果是JAR启动，Copy模板到config目录 ] 
2025-09-01 08:18:47.201 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:33 -  Init Code Generate Template completed in 21 ms
2025-09-01 08:18:47.204 [main] WARN  i.m.prometheusmetrics.PrometheusMeterRegistry:881 - A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.
2025-09-01 08:18:47.204 [main] WARN  i.m.prometheusmetrics.PrometheusMeterRegistry:881 - A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.
2025-09-01 08:18:47.204 [main] WARN  i.m.prometheusmetrics.PrometheusMeterRegistry:881 - A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.
2025-09-01 08:18:47.219 [main] INFO  org.camunda.bpm.engine.jobexecutor:187 - ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].
2025-09-01 08:18:47.220 [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] INFO  org.camunda.bpm.engine.jobexecutor:187 - ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs
2025-09-01 08:18:47.222 [main] INFO  org.jeecg.JeecgSystemApplication:53 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:9092/tld_ai/doc.html
	External: 	http://**************:9092/tld_ai/doc.html
	Swagger文档: 	http://**************:9092/tld_ai/doc.html
----------------------------------------------------------
2025-09-01 08:18:48.097 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:749 - Starting Quartz Scheduler now, after delay of 1 seconds
2025-09-01 08:18:48.111 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3644 - ClusterManager: detected 1 failed or restarted instances.
2025-09-01 08:18:48.111 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3503 - ClusterManager: Scanning for instance "DESKTOP-IGMMD5L1756438449438"'s failed in-progress jobs.
2025-09-01 08:18:48.145 [Quartz Scheduler [MyScheduler]] INFO  org.quartz.core.QuartzScheduler:547 - Scheduler MyScheduler_$_DESKTOP-IGMMD5L1756685919067 started.
2025-09-01 08:26:22.559 [http-nio-9092-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie:168 - A cookie header was received [Hm_lvt_606a102b298cb00317d5a96037729e23=1754894829,1754908301,1755044903;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-09-01 08:26:22.567 [http-nio-9092-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/tld_ai]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-01 08:26:22.567 [http-nio-9092-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-09-01 08:26:22.570 [http-nio-9092-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:554 - Completed initialization in 3 ms
2025-09-01 08:26:22.745 [http-nio-9092-exec-1] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：10ms
2025-09-01 08:26:25.443 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 08:26:25.457 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)23毫秒
2025-09-01 08:26:25.462 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)28毫秒
2025-09-01 08:29:26.012 [http-nio-9092-exec-9] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:31毫秒
2025-09-01 08:29:32.991 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1131 - -------通过数据库读取用户拥有的角色Rules------useId： 1945389941002952705,Roles size: 1
2025-09-01 08:29:33.005 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1162 - -------通过数据库读取用户拥有的权限Perms------userId： 1945389941002952705,Perms size: 141
2025-09-01 08:29:33.005 [http-nio-9092-exec-4] INFO  org.jeecg.config.shiro.ShiroRealm:86 - ===============Shiro权限认证成功==============
2025-09-01 08:29:55.444 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/list],ip:[127.0.0.1]==========
2025-09-01 08:29:55.968 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 08:29:55.973 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========
2025-09-01 08:29:55.978 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 08:29:55.988 [http-nio-9092-exec-9] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 08:29:56.020 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 08:29:56.038 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 08:29:56.038 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 08:29:56.038 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:29:56.140 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 08:29:56.145 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 08:29:56.145 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 08:29:56.146 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:29:57.911 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 08:29:57.915 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:29:58.084 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 08:29:58.088 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 08:29:58.154 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：车间生产管理，ID:1060099867109019648
2025-09-01 08:29:58.282 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 08:29:58.282 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 08:29:58.292 [http-nio-9092-exec-5] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 08:30:04.595 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 08:30:04.598 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 08:30:04.598 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 08:30:04.598 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:30:06.495 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 08:30:06.498 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 08:30:06.498 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 08:30:06.499 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:30:09.293 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 08:30:09.296 [http-nio-9092-exec-2] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:30:09.463 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 08:30:09.466 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 08:30:09.468 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:965205492447150080
2025-09-01 08:30:09.568 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 08:30:09.568 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 08:30:09.574 [http-nio-9092-exec-6] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 08:30:09.721 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 08:30:09.721 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 08:30:22.893 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 08:30:22.897 [http-nio-9092-exec-5] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:30:22.960 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/addVisitsNumber],ip:[127.0.0.1]==========
2025-09-01 08:30:23.049 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 08:30:23.049 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 08:30:40.020 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 08:30:40.023 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:30:40.104 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 08:30:40.106 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 08:30:40.108 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:1060068138562408448
2025-09-01 08:30:40.179 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 08:30:40.179 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 08:30:40.184 [http-nio-9092-exec-9] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 08:30:40.232 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 08:30:40.232 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 08:30:43.658 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 08:30:43.661 [http-nio-9092-exec-7] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTY3NTk0MzN9.Q1KN-FZ76qu56T1yQhvHhMejJCJ8sD5GlTaPhOgb4X4
2025-09-01 08:30:43.742 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 08:30:43.745 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 08:30:43.749 [http-nio-9092-exec-4] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：物业消防巡检状态，ID:993809146154418176
2025-09-01 08:30:43.819 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 08:30:43.819 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 08:30:43.824 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 08:30:43.925 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 08:30:44.536 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getMapDataByCode],ip:[127.0.0.1]==========
2025-09-01 08:30:59.184 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 08:30:59.290 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getMapDataByCode],ip:[127.0.0.1]==========
2025-09-01 08:31:24.600 [http-nio-9092-exec-7] ERROR druid.sql.Statement:157 - {conn-10004, pstmt-20065} execute error. INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 08:31:24.727 [http-nio-9092-exec-7] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 139 common frames omitted
2025-09-01 08:31:39.850 [http-nio-9092-exec-2] ERROR druid.sql.Statement:157 - {conn-10004, pstmt-20066} execute error. INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 08:31:39.854 [http-nio-9092-exec-2] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_role_index (id, role_code, url, component, is_route, status, create_by, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.save(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.add(SysRoleIndexController.java:73)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.add(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 139 common frames omitted
2025-09-01 11:19:31.846 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 11:19:31.866 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)22毫秒
2025-09-01 11:19:31.871 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)27毫秒
2025-09-01 11:54:58.683 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:112毫秒
2025-09-01 14:43:06.754 [http-nio-9092-exec-1] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：5ms
2025-09-01 14:43:07.557 [http-nio-9092-exec-3] INFO  org.jeecg.common.util.filter.SsrfFileTypeFilter:151 - suffix:png
2025-09-01 14:43:07.558 [http-nio-9092-exec-3] WARN  o.jeecg.modules.system.controller.CommonController:232 - 文件[temp/logo_1752650875426.png]不存在..
2025-09-01 15:24:55.937 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:36毫秒
2025-09-01 15:24:55.970 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 15:24:55.986 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)17毫秒
2025-09-01 15:24:55.992 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)23毫秒
2025-09-01 15:25:05.310 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 15:25:05.348 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 15:25:05.392 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 15:25:05.393 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 15:25:05.394 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 15:25:05.408 [http-nio-9092-exec-10] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 15:25:05.419 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-01 15:25:05.419 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 15:25:05.421 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 15:25:05.421 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-01 15:25:05.421 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-01 15:25:05.421 [http-nio-9092-exec-10] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-01 15:25:05.446 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-01 15:25:05.447 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 15:25:05.449 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 15:25:05.450 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-01 15:25:05.450 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-01 15:25:05.450 [http-nio-9092-exec-10] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-01 15:25:54.480 [http-nio-9092-exec-1] INFO  o.jeecg.modules.system.controller.LoginController:202 -  用户名:  演示,退出成功！ 
2025-09-01 15:25:55.118 [http-nio-9092-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = 4cf1502550b38926243b7ff99c0e663fex8f，checkCode = Ex8f
2025-09-01 15:26:06.346 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = ad2307a659ae3c781442be9c08b45b18krrp，checkCode = KRRp
2025-09-01 15:26:11.787 [http-nio-9092-exec-8] INFO  o.j.modules.system.service.impl.SysUserServiceImpl:972 -  登录接口用户的租户ID = 0
2025-09-01 15:26:11.816 [http-nio-9092-exec-4] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：1ms
2025-09-01 15:26:11.823 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:153 - 1 获取用户信息耗时（用户基础信息）2毫秒
2025-09-01 15:26:11.828 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:168 - 2 获取用户信息耗时 (首页面配置)7毫秒
2025-09-01 15:26:11.828 [http-nio-9092-exec-4] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:165 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 15:26:11.839 [http-nio-9092-exec-4] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:184 -       >>> 1 获取系统字典项耗时（SQL）：11毫秒
2025-09-01 15:26:11.840 [http-nio-9092-exec-4] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:188 -       >>> 2 获取系统字典项耗时（Enum）：11毫秒
2025-09-01 15:26:11.840 [http-nio-9092-exec-4] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:190 -       >>> end 获取系统字典库总耗时：12毫秒
2025-09-01 15:26:11.840 [http-nio-9092-exec-4] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:191 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 15:26:11.840 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:172 - 3 获取用户信息耗时 (字典数据)19毫秒
2025-09-01 15:26:11.840 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:177 - end 获取用户信息耗时 19毫秒
2025-09-01 15:26:13.182 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 15:26:13.183 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 15:26:13.184 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 15:26:13.184 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 15:26:13.184 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 15:26:13.184 [http-nio-9092-exec-6] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 15:26:13.427 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 15:26:13.431 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 15:26:13.435 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)8毫秒
2025-09-01 15:26:30.448 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/list],ip:[127.0.0.1]==========
2025-09-01 15:26:30.736 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 15:26:30.739 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========
2025-09-01 15:26:30.742 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:26:30.746 [http-nio-9092-exec-6] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:26:30.755 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 15:26:30.759 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 15:26:30.760 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 15:26:30.760 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTM5NzF9.zIG-kPfyPGp_DprcFUMihcuX06_Z-wGsTiB0Ao5gBzw
2025-09-01 15:26:30.773 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 15:26:30.776 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 15:26:30.776 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 15:26:30.776 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTM5NzF9.zIG-kPfyPGp_DprcFUMihcuX06_Z-wGsTiB0Ao5gBzw
2025-09-01 15:26:35.196 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 15:26:35.199 [http-nio-9092-exec-3] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTM5NzF9.zIG-kPfyPGp_DprcFUMihcuX06_Z-wGsTiB0Ao5gBzw
2025-09-01 15:26:35.352 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 15:26:35.354 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 15:26:35.373 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：物业消防巡检状态，ID:993809146154418176
2025-09-01 15:26:35.472 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 15:26:35.472 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:26:35.475 [http-nio-9092-exec-1] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:26:35.608 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:26:36.003 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getMapDataByCode],ip:[127.0.0.1]==========
2025-09-01 15:27:29.155 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/list],ip:[127.0.0.1]==========
2025-09-01 15:27:29.163 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:27:29.576 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/category/list],ip:[127.0.0.1]==========
2025-09-01 15:27:29.582 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/userinfo],ip:[127.0.0.1]==========
2025-09-01 15:27:29.603 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/query/report/folder],ip:[127.0.0.1]==========
2025-09-01 15:27:34.627 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/index/924614453466595328],ip:[127.0.0.1]==========
2025-09-01 15:27:34.631 [http-nio-9092-exec-8] INFO  org.jeecg.modules.jmreport.desreport.a.a:302 - 进入设计器页面，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTM5NzF9.zIG-kPfyPGp_DprcFUMihcuX06_Z-wGsTiB0Ao5gBzw
2025-09-01 15:27:34.635 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:27:35.369 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/field/tree/924614453466595328],ip:[127.0.0.1]==========
2025-09-01 15:27:35.369 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/category/list],ip:[127.0.0.1]==========
2025-09-01 15:27:35.369 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/link/getLinkData],ip:[127.0.0.1]==========
2025-09-01 15:27:35.369 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 15:27:35.372 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/get/924614453466595328],ip:[127.0.0.1]==========
2025-09-01 15:27:35.470 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/get/924614453466595328],ip:[127.0.0.1]==========
2025-09-01 15:27:41.809 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 15:27:41.811 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTM5NzF9.zIG-kPfyPGp_DprcFUMihcuX06_Z-wGsTiB0Ao5gBzw
2025-09-01 15:27:41.975 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 15:27:41.977 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 15:27:41.978 [http-nio-9092-exec-4] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：示例_统计近十日的登陆次数，ID:1516742733803323394
2025-09-01 15:27:42.052 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:27:42.052 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 15:27:42.055 [http-nio-9092-exec-9] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:27:42.131 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getAllChartData],ip:[127.0.0.1]==========
2025-09-01 15:27:42.187 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getAllChartData],ip:[127.0.0.1]==========
2025-09-01 15:27:42.199 [http-nio-9092-exec-5] INFO  org.jeecg.modules.drag.service.a.f:1333 - 替换系统变量后的执行sql:SELECT
	count(*) num,
	DATE_FORMAT(create_time, '%Y-%m-%d') AS `day`
FROM
	sys_log
WHERE
	log_type = 1
AND create_time > DATE_SUB(NOW(), INTERVAL 10 DAY)
GROUP BY
	DATE_FORMAT(create_time, '%Y-%m-%d')
2025-09-01 15:27:42.201 [http-nio-9092-exec-5] INFO  org.jeecg.modules.drag.service.a.f:578 - 最终执行的DBSQL:SELECT
	count(*) num,
	DATE_FORMAT(create_time, '%Y-%m-%d') AS `day`
FROM
	sys_log
WHERE
	log_type = 1
AND create_time > DATE_SUB(NOW(), INTERVAL 10 DAY)
GROUP BY
	DATE_FORMAT(create_time, '%Y-%m-%d')
2025-09-01 15:27:42.202 [http-nio-9092-exec-2] INFO  org.jeecg.modules.drag.service.a.f:1333 - 替换系统变量后的执行sql:SELECT
	count(*) num,
	DATE_FORMAT(create_time, '%Y-%m-%d') AS `day`
FROM
	sys_log
WHERE
	log_type = 1
AND create_time > DATE_SUB(NOW(), INTERVAL 10 DAY)
GROUP BY
	DATE_FORMAT(create_time, '%Y-%m-%d')
2025-09-01 15:27:42.202 [http-nio-9092-exec-2] INFO  org.jeecg.modules.drag.service.a.f:578 - 最终执行的DBSQL:SELECT
	count(*) num,
	DATE_FORMAT(create_time, '%Y-%m-%d') AS `day`
FROM
	sys_log
WHERE
	log_type = 1
AND create_time > DATE_SUB(NOW(), INTERVAL 10 DAY)
GROUP BY
	DATE_FORMAT(create_time, '%Y-%m-%d')
2025-09-01 15:27:42.217 [http-nio-9092-exec-2] WARN  com.zaxxer.hikari.HikariConfig:1087 - HikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-01 15:27:42.217 [http-nio-9092-exec-5] WARN  com.zaxxer.hikari.HikariConfig:1087 - HikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-01 15:27:42.217 [http-nio-9092-exec-2] INFO  com.zaxxer.hikari.HikariDataSource:109 - HikariCP - Starting...
2025-09-01 15:27:42.217 [http-nio-9092-exec-5] INFO  com.zaxxer.hikari.HikariDataSource:109 - HikariCP - Starting...
2025-09-01 15:27:43.255 [http-nio-9092-exec-2] ERROR org.jeecg.modules.drag.service.a.f:614 - Failed to obtain JDBC Connection
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:476)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:486)
	at org.springframework.jdbc.core.JdbcTemplate.queryForList(JdbcTemplate.java:536)
	at org.jeecg.modules.drag.config.dynamicdb.b.a(OnlDragDynamicDbUtil.java:170)
	at org.jeecg.modules.drag.service.a.f.getChartData(OnlDragDatasetHeadServiceImpl.java:398)
	at org.jeecg.modules.drag.service.a.f.getSqlData(OnlDragDatasetHeadServiceImpl.java:609)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.drag.service.a.f$$SpringCGLIB$$0.getSqlData(<generated>)
	at org.jeecg.modules.drag.b.i.b(OnlDragDatasetHeadController.java:498)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:129)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 104 common frames omitted
2025-09-01 15:27:43.255 [http-nio-9092-exec-5] ERROR org.jeecg.modules.drag.service.a.f:614 - Failed to obtain JDBC Connection
org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:476)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:486)
	at org.springframework.jdbc.core.JdbcTemplate.queryForList(JdbcTemplate.java:536)
	at org.jeecg.modules.drag.config.dynamicdb.b.a(OnlDragDynamicDbUtil.java:170)
	at org.jeecg.modules.drag.service.a.f.getChartData(OnlDragDatasetHeadServiceImpl.java:398)
	at org.jeecg.modules.drag.service.a.f.getSqlData(OnlDragDatasetHeadServiceImpl.java:609)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.drag.service.a.f$$SpringCGLIB$$0.getSqlData(<generated>)
	at org.jeecg.modules.drag.b.i.b(OnlDragDatasetHeadController.java:498)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:129)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 104 common frames omitted
2025-09-01 15:27:47.379 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 15:27:47.381 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTM5NzF9.zIG-kPfyPGp_DprcFUMihcuX06_Z-wGsTiB0Ao5gBzw
2025-09-01 15:27:47.458 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 15:27:47.461 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 15:27:47.464 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:965205492447150080
2025-09-01 15:27:47.533 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:27:47.533 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 15:27:47.537 [http-nio-9092-exec-6] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:27:47.589 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:27:47.589 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 15:28:19.978 [http-nio-9092-exec-3] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1131 - -------通过数据库读取用户拥有的角色Rules------useId： 1945389941002952705,Roles size: 1
2025-09-01 15:28:19.984 [http-nio-9092-exec-3] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1162 - -------通过数据库读取用户拥有的权限Perms------userId： 1945389941002952705,Perms size: 141
2025-09-01 15:28:19.984 [http-nio-9092-exec-3] INFO  org.jeecg.config.shiro.ShiroRealm:86 - ===============Shiro权限认证成功==============
2025-09-01 15:28:20.042 [http-nio-9092-exec-5] INFO  org.jeecg.common.util.filter.SsrfFileTypeFilter:151 - suffix:png
2025-09-01 15:28:20.042 [http-nio-9092-exec-5] WARN  o.jeecg.modules.system.controller.CommonController:232 - 文件[temp/logo_1752650875426.png]不存在..
2025-09-01 15:28:20.103 [http-nio-9092-exec-1] INFO  org.jeecg.common.util.filter.SsrfFileTypeFilter:151 - suffix:png
2025-09-01 15:28:20.103 [http-nio-9092-exec-1] WARN  o.jeecg.modules.system.controller.CommonController:232 - 文件[temp/logo_1752650875426.png]不存在..
2025-09-01 15:28:32.637 [http-nio-9092-exec-7] ERROR druid.sql.Statement:157 - {conn-10003, pstmt-20181} execute error. UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 15:28:32.644 [http-nio-9092-exec-7] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 138 common frames omitted
2025-09-01 15:28:37.128 [http-nio-9092-exec-9] ERROR druid.sql.Statement:157 - {conn-10003, pstmt-20182} execute error. UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 15:28:37.131 [http-nio-9092-exec-9] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 138 common frames omitted
2025-09-01 15:28:40.794 [http-nio-9092-exec-3] ERROR druid.sql.Statement:157 - {conn-10003, pstmt-20183} execute error. UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 15:28:40.796 [http-nio-9092-exec-3] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 138 common frames omitted
2025-09-01 15:28:43.234 [http-nio-9092-exec-5] ERROR druid.sql.Statement:157 - {conn-10003, pstmt-20184} execute error. UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 15:28:43.236 [http-nio-9092-exec-5] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 138 common frames omitted
2025-09-01 15:29:29.662 [http-nio-9092-exec-3] INFO  o.jeecg.modules.system.controller.LoginController:202 -  用户名:  演示,退出成功！ 
2025-09-01 15:29:29.804 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = 629866ed36566652fe98388e9dcb8d52xdwg，checkCode = Xdwg
2025-09-01 15:29:38.153 [http-nio-9092-exec-8] INFO  o.j.modules.system.service.impl.SysUserServiceImpl:972 -  登录接口用户的租户ID = 0
2025-09-01 15:29:38.182 [http-nio-9092-exec-2] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-09-01 15:29:38.190 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:153 - 1 获取用户信息耗时（用户基础信息）4毫秒
2025-09-01 15:29:38.197 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:168 - 2 获取用户信息耗时 (首页面配置)10毫秒
2025-09-01 15:29:38.197 [http-nio-9092-exec-2] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:165 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 15:29:38.204 [http-nio-9092-exec-2] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:184 -       >>> 1 获取系统字典项耗时（SQL）：7毫秒
2025-09-01 15:29:38.205 [http-nio-9092-exec-2] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:188 -       >>> 2 获取系统字典项耗时（Enum）：8毫秒
2025-09-01 15:29:38.205 [http-nio-9092-exec-2] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:190 -       >>> end 获取系统字典库总耗时：8毫秒
2025-09-01 15:29:38.205 [http-nio-9092-exec-2] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:191 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 15:29:38.205 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:172 - 3 获取用户信息耗时 (字典数据)19毫秒
2025-09-01 15:29:38.205 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:177 - end 获取用户信息耗时 19毫秒
2025-09-01 15:29:39.747 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 15:29:39.747 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 15:29:39.748 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 15:29:39.748 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 15:29:39.748 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 15:29:39.748 [http-nio-9092-exec-6] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 15:29:39.900 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 15:29:39.904 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 15:29:39.907 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)7毫秒
2025-09-01 15:38:02.997 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:38:05.294 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:38:16.651 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 15:38:16.651 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 15:38:16.652 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 15:38:16.653 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 15:38:16.653 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 15:38:16.653 [http-nio-9092-exec-2] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 15:43:06.937 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/list],ip:[127.0.0.1]==========
2025-09-01 15:43:07.174 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 15:43:07.175 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========
2025-09-01 15:43:07.179 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:43:07.185 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:43:07.191 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 15:43:07.193 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 15:43:07.193 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 15:43:07.193 [http-nio-9092-exec-1] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTQxNzh9.JYDnSB-x-BolyVWlw1k9e3tEtH25DDEdj9OzJzgB2Sk
2025-09-01 15:43:07.206 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 15:43:07.207 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 15:43:07.208 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 15:43:07.208 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTQxNzh9.JYDnSB-x-BolyVWlw1k9e3tEtH25DDEdj9OzJzgB2Sk
2025-09-01 15:43:34.681 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 15:43:34.683 [http-nio-9092-exec-7] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTQxNzh9.JYDnSB-x-BolyVWlw1k9e3tEtH25DDEdj9OzJzgB2Sk
2025-09-01 15:43:34.769 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 15:43:34.773 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 15:43:34.774 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:1060068138562408448
2025-09-01 15:43:34.852 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:43:34.852 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 15:43:34.856 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:43:34.917 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:43:34.917 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 15:43:37.327 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:43:37.327 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 15:43:47.903 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 15:43:47.905 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTQxNzh9.JYDnSB-x-BolyVWlw1k9e3tEtH25DDEdj9OzJzgB2Sk
2025-09-01 15:43:48.001 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 15:43:48.004 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 15:43:48.005 [http-nio-9092-exec-5] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:1060068138562408448
2025-09-01 15:43:48.059 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:43:48.059 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 15:43:48.064 [http-nio-9092-exec-1] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:43:48.083 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:43:48.083 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 15:43:50.272 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 15:43:50.272 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 15:47:27.932 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/list],ip:[127.0.0.1]==========
2025-09-01 15:47:27.938 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:47:28.293 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/category/list],ip:[127.0.0.1]==========
2025-09-01 15:47:28.295 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/userinfo],ip:[127.0.0.1]==========
2025-09-01 15:47:28.309 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/query/report/folder],ip:[127.0.0.1]==========
2025-09-01 15:47:42.255 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1131 - -------通过数据库读取用户拥有的角色Rules------useId： 1945389941002952705,Roles size: 1
2025-09-01 15:47:42.264 [http-nio-9092-exec-4] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1162 - -------通过数据库读取用户拥有的权限Perms------userId： 1945389941002952705,Perms size: 141
2025-09-01 15:47:42.264 [http-nio-9092-exec-4] INFO  org.jeecg.config.shiro.ShiroRealm:86 - ===============Shiro权限认证成功==============
2025-09-01 15:48:02.593 [http-nio-9092-exec-6] ERROR druid.sql.Statement:157 - {conn-10003, pstmt-20246} execute error. UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at jdk.internal.reflect.GeneratedMethodAccessor363.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-09-01 15:48:02.595 [http-nio-9092-exec-6] ERROR o.jeecg.common.exception.JeecgBootExceptionHandler:193 - 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
### The error may exist in org/jeecg/modules/system/mapper/SysRoleIndexMapper.java (best guess)
### The error may involve org.jeecg.modules.system.mapper.SysRoleIndexMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE sys_role_index SET role_code = ?, url = ?, component = ?, is_route = ?, priority = ?, status = ?, update_by = ?, update_time = ? WHERE id = ?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
; Data truncation: Data too long for column 'url' at row 1
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy192.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:234)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy229.updateById(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.updateById(IRepository.java:139)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at org.jeecg.modules.system.service.impl.SysRoleIndexServiceImpl$$SpringCGLIB$$0.updateById(<generated>)
	at org.jeecg.modules.system.controller.SysRoleIndexController.edit(SysRoleIndexController.java:89)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:64)
	at jdk.internal.reflect.GeneratedMethodAccessor243.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.AutoLogAspect.around(AutoLogAspect.java:58)
	at jdk.internal.reflect.GeneratedMethodAccessor363.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:642)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:632)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at org.jeecg.modules.system.controller.SysRoleIndexController$$SpringCGLIB$$0.edit(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.boot.actuate.web.exchanges.servlet.HttpExchangesFilter.doFilterInternal(HttpExchangesFilter.java:89)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.ssssssss.magicapi.servlet.jakarta.MagicJakartaCorsFilter.doFilter(MagicJakartaCorsFilter.java:15)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'url' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:371)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3462)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3460)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor255.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy410.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at org.jeecg.config.mybatis.MybatisInterceptor.intercept(MybatisInterceptor.java:174)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at jdk.internal.reflect.GeneratedMethodAccessor226.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy409.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 137 common frames omitted
2025-09-01 15:49:38.832 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:9毫秒
2025-09-01 15:49:48.956 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:7毫秒
2025-09-01 15:50:52.815 [http-nio-9092-exec-6] INFO  o.jeecg.modules.system.controller.LoginController:202 -  用户名:  演示,退出成功！ 
2025-09-01 15:50:52.986 [http-nio-9092-exec-10] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = a5b82c4e762344532255373274edc08fpccl，checkCode = pCcL
2025-09-01 15:50:58.277 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = b7c27ecac73628b41bccbdad537e08f983zc，checkCode = 83Zc
2025-09-01 15:51:02.116 [http-nio-9092-exec-3] INFO  o.j.modules.system.service.impl.SysUserServiceImpl:972 -  登录接口用户的租户ID = 0
2025-09-01 15:51:02.133 [http-nio-9092-exec-7] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-09-01 15:51:02.138 [http-nio-9092-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:153 - 1 获取用户信息耗时（用户基础信息）2毫秒
2025-09-01 15:51:02.142 [http-nio-9092-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:168 - 2 获取用户信息耗时 (首页面配置)6毫秒
2025-09-01 15:51:02.142 [http-nio-9092-exec-7] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:165 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 15:51:02.145 [http-nio-9092-exec-7] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:184 -       >>> 1 获取系统字典项耗时（SQL）：3毫秒
2025-09-01 15:51:02.145 [http-nio-9092-exec-7] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:188 -       >>> 2 获取系统字典项耗时（Enum）：3毫秒
2025-09-01 15:51:02.145 [http-nio-9092-exec-7] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:190 -       >>> end 获取系统字典库总耗时：3毫秒
2025-09-01 15:51:02.145 [http-nio-9092-exec-7] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:191 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 15:51:02.145 [http-nio-9092-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:172 - 3 获取用户信息耗时 (字典数据)9毫秒
2025-09-01 15:51:02.145 [http-nio-9092-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:177 - end 获取用户信息耗时 9毫秒
2025-09-01 15:51:03.606 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:10毫秒
2025-09-01 15:51:03.893 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 15:51:03.896 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)3毫秒
2025-09-01 15:51:03.898 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)5毫秒
2025-09-01 15:53:17.449 [http-nio-9092-exec-8] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:7毫秒
2025-09-01 15:54:23.424 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/list],ip:[127.0.0.1]==========
2025-09-01 15:54:23.650 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 15:54:23.653 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========
2025-09-01 15:54:23.655 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 15:54:23.659 [http-nio-9092-exec-1] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:54:23.681 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 15:54:23.683 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 15:54:23.683 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 15:54:23.683 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 15:54:23.693 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 15:54:23.695 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 15:54:23.695 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 15:54:23.695 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 15:54:29.995 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/list],ip:[127.0.0.1]==========
2025-09-01 15:54:29.999 [http-nio-9092-exec-10] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 15:54:30.363 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/category/list],ip:[127.0.0.1]==========
2025-09-01 15:54:30.366 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/userinfo],ip:[127.0.0.1]==========
2025-09-01 15:54:30.379 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/query/report/folder],ip:[127.0.0.1]==========
2025-09-01 15:56:37.319 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 15:56:37.322 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)3毫秒
2025-09-01 15:56:37.323 [http-nio-9092-exec-5] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)4毫秒
2025-09-01 15:56:38.071 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:7毫秒
2025-09-01 16:14:17.380 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/list],ip:[127.0.0.1]==========
2025-09-01 16:14:17.757 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 16:14:17.761 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========
2025-09-01 16:14:17.764 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 16:14:17.792 [http-nio-9092-exec-6] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 16:14:17.807 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 16:14:17.813 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 16:14:17.814 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 16:14:17.814 [http-nio-9092-exec-9] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 16:14:17.834 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 16:14:17.838 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 16:14:17.838 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 16:14:17.838 [http-nio-9092-exec-10] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 16:14:21.227 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 16:14:21.232 [http-nio-9092-exec-3] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 16:14:21.409 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 16:14:21.413 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 16:14:21.509 [http-nio-9092-exec-8] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:1060068138562408448
2025-09-01 16:14:21.609 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 16:14:21.610 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 16:14:21.614 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 16:14:21.769 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 16:14:21.769 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 16:44:54.163 [http-nio-9092-exec-8] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = 11c83f4333d940c4ffcdcdda82c9b676ixcb，checkCode = ixCB
2025-09-01 16:44:58.755 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 16:44:58.757 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 16:44:58.760 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 16:44:58.761 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 16:44:58.761 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 16:44:58.763 [http-nio-9092-exec-5] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 16:44:58.767 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-01 16:44:58.768 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 16:44:58.771 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 16:44:58.771 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-01 16:44:58.771 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-01 16:44:58.771 [http-nio-9092-exec-5] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-01 16:44:58.781 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-01 16:44:58.782 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 16:44:58.784 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 16:44:58.784 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-01 16:44:58.784 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-01 16:44:58.784 [http-nio-9092-exec-5] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-01 16:44:58.786 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 16:44:58.790 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 16:44:58.800 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)14毫秒
2025-09-01 16:45:10.313 [http-nio-9092-exec-9] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:11毫秒
2025-09-01 16:45:15.954 [http-nio-9092-exec-10] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:14毫秒
2025-09-01 16:52:59.177 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/list],ip:[127.0.0.1]==========
2025-09-01 16:52:59.579 [http-nio-9092-exec-10] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/jmreport/dict/list],ip:[127.0.0.1]==========
2025-09-01 16:52:59.581 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/category/list],ip:[127.0.0.1]==========
2025-09-01 16:52:59.584 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 16:52:59.598 [http-nio-9092-exec-5] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 16:52:59.618 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 16:52:59.622 [http-nio-9092-exec-4] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 16:52:59.622 [http-nio-9092-exec-4] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 16:52:59.623 [http-nio-9092-exec-4] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 16:52:59.638 [http-nio-9092-exec-6] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/list],ip:[127.0.0.1]==========
2025-09-01 16:52:59.642 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:73 - ---------自定义页面列表-------req.getParameter.lowAPPId:
2025-09-01 16:52:59.642 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:74 - ---------自定义页面列表-------onlDragPage.getLowAppId:null
2025-09-01 16:52:59.642 [http-nio-9092-exec-6] INFO  org.jeecg.modules.drag.b.m:76 - 进入设计器列表，Saas模式:  ,登录Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 16:53:00.575 [http-nio-9092-exec-2] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/index],ip:[127.0.0.1]==========
2025-09-01 16:53:00.579 [http-nio-9092-exec-2] INFO  org.jeecg.modules.drag.b.a:81 -  index 登录令牌token： eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJleHAiOjE3NTcwMTU0NjJ9.RiQ-6AMy3gIvOSQcIfDdD4Mxuz8QvCXKnekNVn28a-0
2025-09-01 16:53:00.747 [http-nio-9092-exec-7] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/queryById],ip:[127.0.0.1]==========
2025-09-01 16:53:00.751 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/page/getCount],ip:[127.0.0.1]==========
2025-09-01 16:53:00.758 [http-nio-9092-exec-7] INFO  org.jeecg.modules.drag.b.m:316 - 查询仪表盘信息，仪表盘名字：公司年度招聘看板，ID:1060068138562408448
2025-09-01 16:53:00.874 [http-nio-9092-exec-9] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/onlDragDatasetHead/getLoginUser],ip:[127.0.0.1]==========
2025-09-01 16:53:00.874 [http-nio-9092-exec-5] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/comp/treeList],ip:[127.0.0.1]==========
2025-09-01 16:53:00.880 [http-nio-9092-exec-9] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1117 - -------通过数据库读取用户拥有的角色Rules------username： test,Roles size: 1
2025-09-01 16:53:00.942 [http-nio-9092-exec-8] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 16:53:00.942 [http-nio-9092-exec-3] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 16:53:02.502 [http-nio-9092-exec-1] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/city_center],ip:[127.0.0.1]==========
2025-09-01 16:53:02.502 [http-nio-9092-exec-4] INFO  o.j.m.j.c.f.interceptor.JimuReportTokenInterceptor:64 - ==========接收到请求:[/drag/mock/json/china],ip:[127.0.0.1]==========
2025-09-01 16:53:24.847 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1131 - -------通过数据库读取用户拥有的角色Rules------useId： 1945389941002952705,Roles size: 1
2025-09-01 16:53:24.875 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1162 - -------通过数据库读取用户拥有的权限Perms------userId： 1945389941002952705,Perms size: 141
2025-09-01 16:53:24.877 [http-nio-9092-exec-2] INFO  org.jeecg.config.shiro.ShiroRealm:86 - ===============Shiro权限认证成功==============
2025-09-01 16:54:13.388 [http-nio-9092-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:202 -  用户名:  演示,退出成功！ 
2025-09-01 16:54:13.509 [http-nio-9092-exec-5] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = 38dab4744c88feb4a85afd7ecefe797574vh，checkCode = 74VH
2025-09-01 16:54:14.873 [http-nio-9092-exec-7] INFO  o.jeecg.modules.system.controller.LoginController:548 - 获取验证码，Redis key = 5a30ca22bf11fb47a9f0de02d9ff4b52wpdk，checkCode = wPdk
2025-09-01 16:54:25.103 [http-nio-9092-exec-8] INFO  o.j.modules.system.service.impl.SysUserServiceImpl:972 -  登录接口用户的租户ID = 0
2025-09-01 16:54:25.129 [http-nio-9092-exec-10] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-09-01 16:54:25.136 [http-nio-9092-exec-10] INFO  o.jeecg.modules.system.controller.LoginController:153 - 1 获取用户信息耗时（用户基础信息）2毫秒
2025-09-01 16:54:25.139 [http-nio-9092-exec-10] INFO  o.jeecg.modules.system.controller.LoginController:168 - 2 获取用户信息耗时 (首页面配置)5毫秒
2025-09-01 16:54:25.140 [http-nio-9092-exec-10] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:165 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 16:54:25.148 [http-nio-9092-exec-10] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:184 -       >>> 1 获取系统字典项耗时（SQL）：8毫秒
2025-09-01 16:54:25.148 [http-nio-9092-exec-10] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:188 -       >>> 2 获取系统字典项耗时（Enum）：8毫秒
2025-09-01 16:54:25.148 [http-nio-9092-exec-10] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:190 -       >>> end 获取系统字典库总耗时：8毫秒
2025-09-01 16:54:25.148 [http-nio-9092-exec-10] INFO  o.j.modules.system.service.impl.SysDictServiceImpl:191 - >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
2025-09-01 16:54:25.148 [http-nio-9092-exec-10] INFO  o.jeecg.modules.system.controller.LoginController:172 - 3 获取用户信息耗时 (字典数据)14毫秒
2025-09-01 16:54:25.148 [http-nio-9092-exec-10] INFO  o.jeecg.modules.system.controller.LoginController:177 - end 获取用户信息耗时 14毫秒
2025-09-01 16:54:26.100 [http-nio-9092-exec-1] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1131 - -------通过数据库读取用户拥有的角色Rules------useId： 1945389941002952705,Roles size: 1
2025-09-01 16:54:26.111 [http-nio-9092-exec-1] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:1162 - -------通过数据库读取用户拥有的权限Perms------userId： 1945389941002952705,Perms size: 141
2025-09-01 16:54:26.111 [http-nio-9092-exec-1] INFO  org.jeecg.config.shiro.ShiroRealm:86 - ===============Shiro权限认证成功==============
2025-09-01 16:54:26.133 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 16:54:26.137 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 16:54:26.139 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)6毫秒
2025-09-01 18:31:12.352 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 18:31:12.381 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 18:31:12.391 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:31:12.393 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 18:31:12.393 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 18:31:12.397 [http-nio-9092-exec-9] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 18:31:12.407 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-01 18:31:12.410 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 18:31:12.416 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:31:12.417 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-01 18:31:12.418 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-01 18:31:12.418 [http-nio-9092-exec-9] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-01 18:31:12.426 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 18:31:12.435 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-01 18:31:12.437 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 18:31:12.437 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)16毫秒
2025-09-01 18:31:12.442 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:31:12.442 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-01 18:31:12.442 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-01 18:31:12.443 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)22毫秒
2025-09-01 18:31:12.443 [http-nio-9092-exec-9] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-01 18:32:01.669 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 18:32:01.674 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)5毫秒
2025-09-01 18:32:01.679 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)10毫秒
2025-09-01 18:32:01.712 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 18:32:01.714 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 18:32:01.716 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:32:01.717 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 18:32:01.717 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 18:32:01.718 [http-nio-9092-exec-4] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 18:32:09.193 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 18:32:09.194 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 18:32:09.197 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:32:09.198 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 18:32:09.198 [http-nio-9092-exec-9] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 18:32:09.199 [http-nio-9092-exec-9] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 18:32:09.322 [http-nio-9092-exec-10] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 18:32:09.327 [http-nio-9092-exec-10] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)5毫秒
2025-09-01 18:32:09.330 [http-nio-9092-exec-10] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)8毫秒
2025-09-01 18:32:16.149 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 18:32:16.150 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 18:32:16.153 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:32:16.153 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 18:32:16.154 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 18:32:16.154 [http-nio-9092-exec-6] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 18:32:16.305 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 18:32:16.309 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 18:32:16.312 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)7毫秒
2025-09-01 18:37:45.223 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 18:37:45.224 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 18:37:45.229 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:37:45.230 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 18:37:45.230 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 18:37:45.230 [http-nio-9092-exec-6] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 18:37:45.234 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-01 18:37:45.235 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 18:37:45.238 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:37:45.238 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-01 18:37:45.238 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-01 18:37:45.238 [http-nio-9092-exec-6] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-01 18:37:45.244 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-01 18:37:45.246 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-01 18:37:45.247 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:37:45.248 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-01 18:37:45.248 [http-nio-9092-exec-6] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-01 18:37:45.248 [http-nio-9092-exec-6] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-01 18:37:45.437 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 18:37:45.441 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 18:37:45.445 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)8毫秒
2025-09-01 18:37:51.554 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-01 18:37:51.555 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-01 18:37:51.557 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-01 18:37:51.558 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-01 18:37:51.558 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-01 18:37:51.558 [http-nio-9092-exec-10] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-01 18:37:51.704 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-01 18:37:51.707 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)4毫秒
2025-09-01 18:37:51.711 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)8毫秒
2025-09-01 19:29:06.065 [http-nio-9092-exec-2] WARN  org.jeecg.modules.message.websocket.WebSocket:140 - 【系统 WebSocket】消息出现错误
