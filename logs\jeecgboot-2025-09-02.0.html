<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Tue Sep 02 08:06:10 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:10,574</td>
<td class="Message">HV000001: Hibernate Validator 8.0.2.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:10,640</td>
<td class="Message">Starting JeecgSystemApplication using Java 17.0.15 with PID 15252 (D:\wd\3.8.1\boot\jeecg-module-system\jeecg-system-start\target\classes started by Administrator in D:\wd\3.8.1\boot)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:10,640</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">658</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:12,944</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">295</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:12,947</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">143</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,057</td>
<td class="Message">Finished Spring Data repository scanning in 95 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">211</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,162</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,163</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,164</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,365</td>
<td class="Message">Overriding bean definition for bean &#39;classifier&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,366</td>
<td class="Message">Overriding bean definition for bean &#39;end&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.b; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/b.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,367</td>
<td class="Message">Overriding bean definition for bean &#39;http&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.c; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/c.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,367</td>
<td class="Message">Overriding bean definition for bean &#39;knowledge&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.d; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/d.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,368</td>
<td class="Message">Overriding bean definition for bean &#39;llm&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.e; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/e.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,368</td>
<td class="Message">Overriding bean definition for bean &#39;enhanceJava&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.enhance.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/enhance/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,369</td>
<td class="Message">Overriding bean definition for bean &#39;reply&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.f; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/f.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,369</td>
<td class="Message">Overriding bean definition for bean &#39;start&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.g; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/g.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,373</td>
<td class="Message">Overriding bean definition for bean &#39;subflow&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.h; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/h.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,374</td>
<td class="Message">Overriding bean definition for bean &#39;switch&#39; with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.i; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/i.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]</td>
<td class="MethodOfCaller">logBeanDefinitionOverriding</td>
<td class="FileOfCaller">DefaultListableBeanFactory.java</td>
<td class="LineOfCaller">1333</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,800</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportCategoryDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,800</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,801</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,801</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,801</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportJobDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportLogDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.JimuDragCategoryDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.JimuDragMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.JimuReportIconLibDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,802</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,803</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:13,803</td>
<td class="Message">register minidao name is { org.jeecg.modules.drag.dao.OnlDragShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:13,805</td>
<td class="Message">Cannot enhance @Configuration bean definition &#39;com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration&#39; since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as &#39;static&#39; and/or marking the containing configuration class as &#39;proxyBeanMethods=false&#39;.</td>
<td class="MethodOfCaller">enhanceConfigurationClasses</td>
<td class="FileOfCaller">ConfigurationClassPostProcessor.java</td>
<td class="LineOfCaller">514</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:14,655</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:14,658</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">314</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,028</td>
<td class="Message">Tomcat initialized with port 9092 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,042</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-9092&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,044</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,044</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/10.1.40]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,112</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,112</td>
<td class="Message">Root WebApplicationContext: initialization completed in 4223 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">301</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,198</td>
<td class="Message">Configuring camunda rest api.</td>
<td class="MethodOfCaller">registerCamundaRestResources</td>
<td class="FileOfCaller">CamundaJerseyResourceConfig.java</td>
<td class="LineOfCaller">38</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,217</td>
<td class="Message">Finished configuring camunda rest api.</td>
<td class="MethodOfCaller">registerCamundaRestResources</td>
<td class="FileOfCaller">CamundaJerseyResourceConfig.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,356</td>
<td class="Message"> Init JimuReport Config [ Token Interceptor &amp; Resource Locations ] </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,361</td>
<td class="Message">
================================================================================================
            _ _ __  __       ____                       _   
           | (_)  \/  |_   _|  _ \ ___ _ __   ___  _ __| |_ 
        _  | | | |\/| | | | | |_) / _ \ &#39;_ \ / _ \| &#39;__| __|
       | |_| | | |  | | |_| |  _ &lt;  __/ |_) | (_) | |  | |_ 
        \___/|_|_|  |_|\__,_|_| \_\___| .__/ \___/|_|   \__|
                                      |_|                   
		Version: 2.0.0
		打造 “简单|智能|专业 ” 的数据可视化报表。
		官网：https://jimureport.com
================================================================================================
</td>
<td class="MethodOfCaller">print</td>
<td class="FileOfCaller">LogoPrinter.java</td>
<td class="LineOfCaller">34</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,911</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">673</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,912</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:15,913</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:17,537</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,209</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,211</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,221</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,222</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,225</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,227</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,228</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-IGMMD5L1756771580211&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,228</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,228</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:20,228</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@bb832f0</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,137</td>
<td class="Message">component aspect implement[nodeProcessAspect] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">CmpAroundAspectBeanProcess.java</td>
<td class="LineOfCaller">32</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,510</td>
<td class="Message">proxy component[classifier] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,526</td>
<td class="Message">proxy component[end] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,541</td>
<td class="Message">proxy component[http] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,551</td>
<td class="Message">proxy component[knowledge] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,565</td>
<td class="Message">proxy component[llm] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,575</td>
<td class="Message">proxy component[enhanceJava] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,585</td>
<td class="Message">proxy component[reply] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,592</td>
<td class="Message">proxy component[start] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,601</td>
<td class="Message">proxy component[subflow] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:24,611</td>
<td class="Message">proxy component[switch] has been found</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">DeclWarpBeanProcess.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:25,813</td>
<td class="Message"> Init JimuReport Config [ 线程池 ] </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:26,038</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">a</td>
<td class="FileOfCaller">JmDragRedisConfig.java</td>
<td class="LineOfCaller">31</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:26,942</td>
<td class="Message">STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,264</td>
<td class="Message">ENGINE-12003 Plugin &#39;CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, createAdminUserConfiguration, failedJobConfiguration, CreateFilterConfiguration[filterName=All tasks], org.jeecg.config.CamundaConfiguration$1@6a92832, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin, SpringBootSpinProcessEnginePlugin]&#39; activated on process engine &#39;default&#39;</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,277</td>
<td class="Message">EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,279</td>
<td class="Message">EVENTING-003: Task events will be published as Spring Events.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,279</td>
<td class="Message">EVENTING-005: Execution events will be published as Spring Events.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,279</td>
<td class="Message">EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,287</td>
<td class="Message">EVENTING-007: History events will be published as Spring events.</td>
<td class="MethodOfCaller">preInit</td>
<td class="FileOfCaller">EventPublisherPlugin.java</td>
<td class="LineOfCaller">78</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,292</td>
<td class="Message">SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormatProvider[name = application/json]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,540</td>
<td class="Message">SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormatProvider[name = application/xml]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,665</td>
<td class="Message">SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormat[name = application/xml]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,665</td>
<td class="Message">SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormat[name = application/json]</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:27,969</td>
<td class="Message">FEEL/SCALA-01001 Spin value mapper detected</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:28,116</td>
<td class="Message">ENGINE-12020 The transaction isolation level set for the database is &#39;REPEATABLE_READ&#39; which differs from the recommended value and property skipIsolationLevelCheck is enabled. Please keep in mind that levels different from &#39;READ_COMMITTED&#39; are known to cause deadlocks and other unexpected behaviours.</td>
<td class="MethodOfCaller">logWarn</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:30,053</td>
<td class="Message">ENGINE-00001 Process Engine default created.</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:30,123</td>
<td class="Message">STARTER-SB016 Skip initial filter creation, the filter with this name already exists: All tasks</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:31,411</td>
<td class="Message">Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(jakarta.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final, consider using interface-based JDK proxies instead.</td>
<td class="MethodOfCaller">doValidateClass</td>
<td class="FileOfCaller">CglibAopProxy.java</td>
<td class="LineOfCaller">296</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:31,412</td>
<td class="Message">Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(jakarta.servlet.ServletContext)] because it is marked as final, consider using interface-based JDK proxies instead.</td>
<td class="MethodOfCaller">doValidateClass</td>
<td class="FileOfCaller">CglibAopProxy.java</td>
<td class="LineOfCaller">296</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:31,413</td>
<td class="Message">Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final, consider using interface-based JDK proxies instead.</td>
<td class="MethodOfCaller">doValidateClass</td>
<td class="FileOfCaller">CglibAopProxy.java</td>
<td class="LineOfCaller">296</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:31,945</td>
<td class="Message">Init Token ignoreAuthUrls Config [ 集合 ]  ：[/test/jeecgDemo/html, /openapi/demo/index, /airag/flow/run, /airag/flow/run/*, /airag/app/queryById, /airag/chat/stop/*, /airag/chat/send, /airag/chat/upload, /airag/chat/conversation/update/title, /airag/chat/messages/clear/*, /airag/chat/init, /airag/chat/conversations, /airag/chat/conversation/*, /airag/chat/messages]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">IgnoreAuthPostProcessor.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:31,947</td>
<td class="Message">Init Token ignoreAuthUrls Config [ 耗时 ] ：6毫秒</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">IgnoreAuthPostProcessor.java</td>
<td class="LineOfCaller">49</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:31,996</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:33,097</td>
<td class="Message">Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@65327f5</td>
<td class="MethodOfCaller">setApplicationContext</td>
<td class="FileOfCaller">MybatisPlusApplicationContextAware.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:33,125</td>
<td class="Message">lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazyProcessEnginesFilter@3a235a93</td>
<td class="MethodOfCaller">lazyInit</td>
<td class="FileOfCaller">LazyInitRegistration.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:33,174</td>
<td class="Message">lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazySecurityFilter@46a6368f</td>
<td class="MethodOfCaller">lazyInit</td>
<td class="FileOfCaller">LazyInitRegistration.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:34,852</td>
<td class="Message">未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件</td>
<td class="MethodOfCaller">magicNotifyService</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">221</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:34,928</td>
<td class="Message">注册数据源：default</td>
<td class="MethodOfCaller">put</td>
<td class="FileOfCaller">MagicDynamicDataSource.java</td>
<td class="LineOfCaller">67</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:34,931</td>
<td class="Message">未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)</td>
<td class="MethodOfCaller">pageProvider</td>
<td class="FileOfCaller">MagicModuleConfiguration.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:34,933</td>
<td class="Message">未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)</td>
<td class="MethodOfCaller">sqlCache</td>
<td class="FileOfCaller">MagicModuleConfiguration.java</td>
<td class="LineOfCaller">122</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,123</td>
<td class="Message">magic-api工作目录:db://api_file/magic-api</td>
<td class="MethodOfCaller">magicConfiguration</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">302</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,134</td>
<td class="Message">注册模块:log -&gt; interface org.slf4j.Logger</td>
<td class="MethodOfCaller">setupMagicModules</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">267</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,143</td>
<td class="Message">注册模块:db -&gt; class org.ssssssss.magicapi.modules.db.SQLModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,144</td>
<td class="Message">注册模块:http -&gt; class org.ssssssss.magicapi.modules.http.HttpModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,144</td>
<td class="Message">注册模块:env -&gt; class org.ssssssss.magicapi.modules.spring.EnvModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,145</td>
<td class="Message">注册模块:request -&gt; class org.ssssssss.magicapi.modules.servlet.RequestModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,145</td>
<td class="Message">注册模块:response -&gt; class org.ssssssss.magicapi.modules.servlet.ResponseModule</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,145</td>
<td class="Message">注册模块:magic -&gt; class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService</td>
<td class="MethodOfCaller">lambda$setupMagicModules$7</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,145</td>
<td class="Message">自动导入模块：db</td>
<td class="MethodOfCaller">lambda$setupMagicModules$8</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">280</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,150</td>
<td class="Message">注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -&gt; class org.ssssssss.magicapi.servlet.jakarta.MagicJakartaResponseExtension</td>
<td class="MethodOfCaller">lambda$setupMagicModules$10</td>
<td class="FileOfCaller">MagicAPIAutoConfiguration.java</td>
<td class="LineOfCaller">288</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,194</td>
<td class="Message">Exposing 3 endpoints beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">60</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,522</td>
<td class="Message">flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser</td>
<td class="MethodOfCaller">info</td>
<td class="FileOfCaller">LFLog.java</td>
<td class="LineOfCaller">193</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,565</td>
<td class="Message">query sql: select id, application_name, chain from airag_flow where status = &#39;enable&#39; and chain is not null</td>
<td class="MethodOfCaller">info</td>
<td class="FileOfCaller">LFLog.java</td>
<td class="LineOfCaller">186</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,582</td>
<td class="Message">use dataSourceName[dataSource],has found liteflow config</td>
<td class="MethodOfCaller">getConn</td>
<td class="FileOfCaller">LiteFlowJdbcUtil.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,878</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-9092&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:35,891</td>
<td class="Message">Tomcat started on port 9092 (http) with context path &#39;/tld_ai&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">243</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,537</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">735</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,567</td>
<td class="Message">Started JeecgSystemApplication in 26.456 seconds (process running for 27.212)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,694</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">30</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,757</td>
<td class="Message"> Init Code Generate Template completed in 63 ms</td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">33</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:36,760</td>
<td class="Message">A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.</td>
<td class="MethodOfCaller">logWarningAboutLateFilter</td>
<td class="FileOfCaller">MeterRegistry.java</td>
<td class="LineOfCaller">881</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:36,761</td>
<td class="Message">A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.</td>
<td class="MethodOfCaller">logWarningAboutLateFilter</td>
<td class="FileOfCaller">MeterRegistry.java</td>
<td class="LineOfCaller">881</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-09-02 08:06:36,761</td>
<td class="Message">A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.</td>
<td class="MethodOfCaller">logWarningAboutLateFilter</td>
<td class="FileOfCaller">MeterRegistry.java</td>
<td class="LineOfCaller">881</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,792</td>
<td class="Message">ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,794</td>
<td class="Message">ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs</td>
<td class="MethodOfCaller">logInfo</td>
<td class="FileOfCaller">BaseLogger.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:36,799</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:9092/tld_ai/doc.html
	External: 	http://**************:9092/tld_ai/doc.html
	Swagger文档: 	http://**************:9092/tld_ai/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">53</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,295</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,297</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">532</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,299</td>
<td class="Message">Completed initialization in 2 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">554</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,549</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">lambda$startScheduler$0</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">749</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,602</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,602</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-IGMMD5L1756685919067&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:06:37,618</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-IGMMD5L1756771580211 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:07,661</td>
<td class="Message">加密操作，Aspect程序耗时：12ms</td>
<td class="MethodOfCaller">around</td>
<td class="FileOfCaller">SensitiveDataAspect.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,140</td>
<td class="Message">字典拼接的查询SQL：select realname,username from sys_user</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,147</td>
<td class="Message">获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName=&#39;sys_user&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,160</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,161</td>
<td class="Message">checkWhiteList tableName: sys_user</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,161</td>
<td class="Message">白名单校验：查询表&quot;sys_user&quot;，查询字段 [realname, username] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,163</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_user&#39;, alias=&#39;&#39;, fields=[realname, username], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,167</td>
<td class="Message">字典拼接的查询SQL：select name,id from airag_flow where status = &#39;enable&#39; </td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,168</td>
<td class="Message">获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName=&#39;airag_flow&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,170</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,170</td>
<td class="Message">checkWhiteList tableName: airag_flow</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,170</td>
<td class="Message">白名单校验：查询表&quot;airag_flow&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,170</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;airag_flow&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,175</td>
<td class="Message">字典拼接的查询SQL：select name,id from airag_model where model_type = &#39;LLM&#39; </td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,176</td>
<td class="Message">获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName=&#39;airag_model&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,178</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,178</td>
<td class="Message">checkWhiteList tableName: airag_model</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,178</td>
<td class="Message">白名单校验：查询表&quot;airag_model&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,178</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;airag_model&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,714</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,739</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)31毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:23:13,744</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)36毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:50:13,232</td>
<td class="Message">======获取全部菜单数据=====耗时:32毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">114</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,291</td>
<td class="Message">字典拼接的查询SQL：select realname,username from sys_user</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,292</td>
<td class="Message">获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName=&#39;sys_user&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,295</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,295</td>
<td class="Message">checkWhiteList tableName: sys_user</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,295</td>
<td class="Message">白名单校验：查询表&quot;sys_user&quot;，查询字段 [realname, username] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,295</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_user&#39;, alias=&#39;&#39;, fields=[realname, username], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,299</td>
<td class="Message">字典拼接的查询SQL：select name,id from airag_flow where status = &#39;enable&#39; </td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,300</td>
<td class="Message">获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName=&#39;airag_flow&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,301</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,302</td>
<td class="Message">checkWhiteList tableName: airag_flow</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,302</td>
<td class="Message">白名单校验：查询表&quot;airag_flow&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,302</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;airag_flow&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,306</td>
<td class="Message">字典拼接的查询SQL：select name,id from airag_model where model_type = &#39;LLM&#39; </td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,307</td>
<td class="Message">获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName=&#39;airag_model&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,309</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,310</td>
<td class="Message">checkWhiteList tableName: airag_model</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,310</td>
<td class="Message">白名单校验：查询表&quot;airag_model&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,310</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;airag_model&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,538</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,544</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)6毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:55:28,549</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)11毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,484</td>
<td class="Message">字典拼接的查询SQL：select realname,username from sys_user</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,485</td>
<td class="Message">获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName=&#39;sys_user&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,488</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,488</td>
<td class="Message">checkWhiteList tableName: sys_user</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,488</td>
<td class="Message">白名单校验：查询表&quot;sys_user&quot;，查询字段 [realname, username] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,489</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_user&#39;, alias=&#39;&#39;, fields=[realname, username], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,663</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,669</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)6毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 08:56:11,673</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)10毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,077</td>
<td class="Message">字典拼接的查询SQL：select realname,username from sys_user</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,079</td>
<td class="Message">获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName=&#39;sys_user&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,081</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,082</td>
<td class="Message">checkWhiteList tableName: sys_user</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,082</td>
<td class="Message">白名单校验：查询表&quot;sys_user&quot;，查询字段 [realname, username] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,082</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_user&#39;, alias=&#39;&#39;, fields=[realname, username], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,086</td>
<td class="Message">字典拼接的查询SQL：select name,id from airag_flow where status = &#39;enable&#39; </td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,087</td>
<td class="Message">获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName=&#39;airag_flow&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,089</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,090</td>
<td class="Message">checkWhiteList tableName: airag_flow</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,090</td>
<td class="Message">白名单校验：查询表&quot;airag_flow&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,090</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;airag_flow&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,095</td>
<td class="Message">字典拼接的查询SQL：select name,id from airag_model where model_type = &#39;LLM&#39; </td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,096</td>
<td class="Message">获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName=&#39;airag_model&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,099</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,100</td>
<td class="Message">checkWhiteList tableName: airag_model</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,100</td>
<td class="Message">白名单校验：查询表&quot;airag_model&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,100</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;airag_model&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,214</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,220</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)6毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:35,227</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)13毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:39,834</td>
<td class="Message">字典拼接的查询SQL：select realname,username from sys_user</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:39,835</td>
<td class="Message">获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName=&#39;sys_user&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:39,838</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:39,838</td>
<td class="Message">checkWhiteList tableName: sys_user</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:39,838</td>
<td class="Message">白名单校验：查询表&quot;sys_user&quot;，查询字段 [realname, username] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:39,839</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_user&#39;, alias=&#39;&#39;, fields=[realname, username], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:40,079</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:40,084</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)5毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:45:40,090</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)11毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,709</td>
<td class="Message">字典拼接的查询SQL：select realname,username from sys_user</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,711</td>
<td class="Message">获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName=&#39;sys_user&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,714</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,716</td>
<td class="Message">checkWhiteList tableName: sys_user</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,717</td>
<td class="Message">白名单校验：查询表&quot;sys_user&quot;，查询字段 [realname, username] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,717</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_user&#39;, alias=&#39;&#39;, fields=[realname, username], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,848</td>
<td class="Message">-----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">348</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,853</td>
<td class="Message">begin 获取用户近2个月的系统公告 (通知)5毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-09-02 09:48:24,859</td>
<td class="Message">end 获取用户2个月的系统公告 (系统消息)11毫秒</td>
<td class="MethodOfCaller">listByUser</td>
<td class="FileOfCaller">SysAnnouncementController.java</td>
<td class="LineOfCaller">371</td>
</tr>
