2025-09-02 08:06:10.574 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 8.0.2.Final
2025-09-02 08:06:10.640 [main] INFO  org.jeecg.JeecgSystemApplication:53 - Starting JeecgSystemApplication using Java 17.0.15 with PID 15252 (D:\wd\3.8.1\boot\jeecg-module-system\jeecg-system-start\target\classes started by Administrator in D:\wd\3.8.1\boot)
2025-09-02 08:06:10.640 [main] INFO  org.jeecg.JeecgSystemApplication:658 - The following 1 profile is active: "dev"
2025-09-02 08:06:12.944 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:295 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-02 08:06:12.947 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:143 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 08:06:13.057 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:211 - Finished Spring Data repository scanning in 95 ms. Found 0 Redis repository interfaces.
2025-09-02 08:06:13.162 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-09-02 08:06:13.163 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
2025-09-02 08:06:13.164 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-09-02 08:06:13.365 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'classifier' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.366 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'end' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.b; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/b.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.367 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'http' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.c; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/c.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.367 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'knowledge' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.d; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/d.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.368 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'llm' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.e; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/e.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.368 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'enhanceJava' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.enhance.a; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/enhance/a.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.369 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'reply' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.f; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/f.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.369 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'start' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.g; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/g.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.373 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'subflow' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.h; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/h.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.374 [main] INFO  o.s.b.factory.support.DefaultListableBeanFactory:1333 - Overriding bean definition for bean 'switch' with a different definition: replacing [Generic bean: class=org.jeecg.modules.airag.flow.component.i; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null; defined in URL [jar:file:/D:/JAVA_DEV/repo/org/jeecgframework/boot3/jeecg-aiflow/1.1.1/jeecg-aiflow-1.1.1.jar!/org/jeecg/modules/airag/flow/component/i.class]] with [Generic bean: class=com.yomahub.liteflow.core.proxy.DeclWarpBean; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=null; factoryMethodName=null; initMethodNames=null; destroyMethodNames=null]
2025-09-02 08:06:13.800 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportCategoryDao }
2025-09-02 08:06:13.800 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-09-02 08:06:13.801 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-09-02 08:06:13.801 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-09-02 08:06:13.801 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportJobDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportExportLogDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.JimuDragCategoryDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.JimuDragMapDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.JimuReportIconLibDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragCompDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDataSourceDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetHeadDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetItemDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragDatasetParamDao }
2025-09-02 08:06:13.802 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageCompDao }
2025-09-02 08:06:13.803 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragPageDao }
2025-09-02 08:06:13.803 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:55 - register minidao name is { org.jeecg.modules.drag.dao.OnlDragShareDao }
2025-09-02 08:06:13.805 [main] WARN  o.s.c.annotation.ConfigurationClassPostProcessor:514 - Cannot enhance @Configuration bean definition 'com.yomahub.liteflow.springboot.config.LiteflowMainAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-09-02 08:06:14.655 [main] INFO  org.jeecg.config.shiro.ShiroConfig:293 - ===============(1)创建缓存管理器RedisCacheManager
2025-09-02 08:06:14.658 [main] INFO  org.jeecg.config.shiro.ShiroConfig:314 - ===============(2)创建RedisManager,连接Redis..
2025-09-02 08:06:15.028 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:111 - Tomcat initialized with port 9092 (http)
2025-09-02 08:06:15.042 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-9092"]
2025-09-02 08:06:15.044 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-09-02 08:06:15.044 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/10.1.40]
2025-09-02 08:06:15.112 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/tld_ai]:168 - Initializing Spring embedded WebApplicationContext
2025-09-02 08:06:15.112 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 4223 ms
2025-09-02 08:06:15.198 [main] INFO  o.c.b.s.b.starter.rest.CamundaJerseyResourceConfig:38 - Configuring camunda rest api.
2025-09-02 08:06:15.217 [main] INFO  o.c.b.s.b.starter.rest.CamundaJerseyResourceConfig:44 - Finished configuring camunda rest api.
2025-09-02 08:06:15.356 [main] INFO  o.j.m.jmreport.config.init.JimuReportConfiguration:130 -  Init JimuReport Config [ Token Interceptor & Resource Locations ] 
2025-09-02 08:06:15.361 [main] INFO  o.jeecg.modules.jmreport.config.init.LogoPrinter:34 - 
================================================================================================
            _ _ __  __       ____                       _   
           | (_)  \/  |_   _|  _ \ ___ _ __   ___  _ __| |_ 
        _  | | | |\/| | | | | |_) / _ \ '_ \ / _ \| '__| __|
       | |_| | | |  | | |_| |  _ <  __/ |_) | (_) | |  | |_ 
        \___/|_|_|  |_|\__,_|_| \_\___| .__/ \___/|_|   \__|
                                      |_|                   
		Version: 2.0.0
		打造 “简单|智能|专业 ” 的数据可视化报表。
		官网：https://jimureport.com
================================================================================================

2025-09-02 08:06:15.911 [main] INFO  com.alibaba.druid.pool.DruidDataSource:673 - {dataSource-1,master} inited
2025-09-02 08:06:15.912 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-09-02 08:06:15.913 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-09-02 08:06:17.537 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:53 -  --- redis config init --- 
2025-09-02 08:06:20.209 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-09-02 08:06:20.211 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-09-02 08:06:20.221 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-09-02 08:06:20.222 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-09-02 08:06:20.225 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-09-02 08:06:20.227 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-09-02 08:06:20.228 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-IGMMD5L1756771580211'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-09-02 08:06:20.228 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-09-02 08:06:20.228 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-09-02 08:06:20.228 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@bb832f0
2025-09-02 08:06:24.137 [main] INFO  c.y.l.process.impl.CmpAroundAspectBeanProcess:32 - component aspect implement[nodeProcessAspect] has been found
2025-09-02 08:06:24.510 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[classifier] has been found
2025-09-02 08:06:24.526 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[end] has been found
2025-09-02 08:06:24.541 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[http] has been found
2025-09-02 08:06:24.551 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[knowledge] has been found
2025-09-02 08:06:24.565 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[llm] has been found
2025-09-02 08:06:24.575 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[enhanceJava] has been found
2025-09-02 08:06:24.585 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[reply] has been found
2025-09-02 08:06:24.592 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[start] has been found
2025-09-02 08:06:24.601 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[subflow] has been found
2025-09-02 08:06:24.611 [main] INFO  c.y.liteflow.process.impl.DeclWarpBeanProcess:37 - proxy component[switch] has been found
2025-09-02 08:06:25.813 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:42 -  Init JimuReport Config [ 线程池 ] 
2025-09-02 08:06:26.038 [main] INFO  org.jeecg.modules.drag.config.a.b.a:31 -  --- redis config init --- 
2025-09-02 08:06:26.942 [main] INFO  org.camunda.bpm.spring.boot:187 - STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10
2025-09-02 08:06:27.264 [main] INFO  org.camunda.bpm.engine.cfg:187 - ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, createAdminUserConfiguration, failedJobConfiguration, CreateFilterConfiguration[filterName=All tasks], org.jeecg.config.CamundaConfiguration$1@6a92832, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin, SpringBootSpinProcessEnginePlugin]' activated on process engine 'default'
2025-09-02 08:06:27.277 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:56 - EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-09-02 08:06:27.279 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:58 - EVENTING-003: Task events will be published as Spring Events.
2025-09-02 08:06:27.279 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:64 - EVENTING-005: Execution events will be published as Spring Events.
2025-09-02 08:06:27.279 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:69 - EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-09-02 08:06:27.287 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin:78 - EVENTING-007: History events will be published as Spring events.
2025-09-02 08:06:27.292 [main] INFO  org.camunda.spin:187 - SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormatProvider[name = application/json]
2025-09-02 08:06:27.540 [main] INFO  org.camunda.spin:187 - SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormatProvider[name = application/xml]
2025-09-02 08:06:27.665 [main] INFO  org.camunda.spin:187 - SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormat[name = application/xml]
2025-09-02 08:06:27.665 [main] INFO  org.camunda.spin:187 - SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormat[name = application/json]
2025-09-02 08:06:27.969 [main] INFO  org.camunda.bpm.dmn.feel.scala:187 - FEEL/SCALA-01001 Spin value mapper detected
2025-09-02 08:06:28.116 [main] WARN  org.camunda.bpm.engine.cfg:201 - ENGINE-12020 The transaction isolation level set for the database is 'REPEATABLE_READ' which differs from the recommended value and property skipIsolationLevelCheck is enabled. Please keep in mind that levels different from 'READ_COMMITTED' are known to cause deadlocks and other unexpected behaviours.
2025-09-02 08:06:30.053 [main] INFO  org.camunda.bpm.engine:187 - ENGINE-00001 Process Engine default created.
2025-09-02 08:06:30.123 [main] INFO  org.camunda.bpm.spring.boot:187 - STARTER-SB016 Skip initial filter creation, the filter with this name already exists: All tasks
2025-09-02 08:06:31.411 [main] WARN  org.springframework.aop.framework.CglibAopProxy:296 - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(jakarta.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-02 08:06:31.412 [main] WARN  org.springframework.aop.framework.CglibAopProxy:296 - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(jakarta.servlet.ServletContext)] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-02 08:06:31.413 [main] WARN  org.springframework.aop.framework.CglibAopProxy:296 - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-02 08:06:31.945 [main] INFO  o.j.config.shiro.ignore.IgnoreAuthPostProcessor:41 - Init Token ignoreAuthUrls Config [ 集合 ]  ：[/test/jeecgDemo/html, /openapi/demo/index, /airag/flow/run, /airag/flow/run/*, /airag/app/queryById, /airag/chat/stop/*, /airag/chat/send, /airag/chat/upload, /airag/chat/conversation/update/title, /airag/chat/messages/clear/*, /airag/chat/init, /airag/chat/conversations, /airag/chat/conversation/*, /airag/chat/messages]
2025-09-02 08:06:31.947 [main] INFO  o.j.config.shiro.ignore.IgnoreAuthPostProcessor:49 - Init Token ignoreAuthUrls Config [ 耗时 ] ：6毫秒
2025-09-02 08:06:31.996 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:50 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-09-02 08:06:33.097 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware:40 - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@65327f5
2025-09-02 08:06:33.125 [main] INFO  o.c.b.s.b.s.webapp.filter.LazyInitRegistration:66 - lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazyProcessEnginesFilter@3a235a93
2025-09-02 08:06:33.174 [main] INFO  o.c.b.s.b.s.webapp.filter.LazyInitRegistration:66 - lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazySecurityFilter@46a6368f
2025-09-02 08:06:34.852 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:221 - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-09-02 08:06:34.928 [main] INFO  o.s.m.datasource.model.MagicDynamicDataSource:67 - 注册数据源：default
2025-09-02 08:06:34.931 [main] INFO  o.s.m.spring.boot.starter.MagicModuleConfiguration:111 - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
2025-09-02 08:06:34.933 [main] INFO  o.s.m.spring.boot.starter.MagicModuleConfiguration:122 - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-09-02 08:06:35.123 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:302 - magic-api工作目录:db://api_file/magic-api
2025-09-02 08:06:35.134 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:267 - 注册模块:log -> interface org.slf4j.Logger
2025-09-02 08:06:35.143 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-09-02 08:06:35.144 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-09-02 08:06:35.144 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-09-02 08:06:35.145 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-09-02 08:06:35.145 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-09-02 08:06:35.145 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:272 - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-09-02 08:06:35.145 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:280 - 自动导入模块：db
2025-09-02 08:06:35.150 [main] INFO  o.s.m.s.boot.starter.MagicAPIAutoConfiguration:288 - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.jakarta.MagicJakartaResponseExtension
2025-09-02 08:06:35.194 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:60 - Exposing 3 endpoints beneath base path '/actuator'
2025-09-02 08:06:35.522 [main] INFO  c.y.liteflow.parser.factory.FlowParserProvider:193 - flow info loaded from class config with el,class=com.yomahub.liteflow.parser.sql.SQLXmlELParser
2025-09-02 08:06:35.565 [main] INFO  c.yomahub.liteflow.parser.sql.read.AbstractSqlRead:186 - query sql: select id, application_name, chain from airag_flow where status = 'enable' and chain is not null
2025-09-02 08:06:35.582 [main] INFO  c.y.liteflow.parser.sql.util.LiteFlowJdbcUtil:43 - use dataSourceName[dataSource],has found liteflow config
2025-09-02 08:06:35.878 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-9092"]
2025-09-02 08:06:35.891 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:243 - Tomcat started on port 9092 (http) with context path '/tld_ai'
2025-09-02 08:06:36.537 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:735 - Will start Quartz Scheduler [MyScheduler] in 1 seconds
2025-09-02 08:06:36.567 [main] INFO  org.jeecg.JeecgSystemApplication:59 - Started JeecgSystemApplication in 26.456 seconds (process running for 27.212)
2025-09-02 08:06:36.694 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:30 -  Init Code Generate Template [ 检测如果是JAR启动，Copy模板到config目录 ] 
2025-09-02 08:06:36.757 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:33 -  Init Code Generate Template completed in 63 ms
2025-09-02 08:06:36.760 [main] WARN  i.m.prometheusmetrics.PrometheusMeterRegistry:881 - A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.
2025-09-02 08:06:36.761 [main] WARN  i.m.prometheusmetrics.PrometheusMeterRegistry:881 - A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.
2025-09-02 08:06:36.761 [main] WARN  i.m.prometheusmetrics.PrometheusMeterRegistry:881 - A MeterFilter is being configured after a Meter has been registered to this registry. All MeterFilters should be configured before any Meters are registered. If that is not possible or you have a use case where it should be allowed, let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920. Enable DEBUG level logging on this logger to see a stack trace of the call configuring this MeterFilter.
2025-09-02 08:06:36.792 [main] INFO  org.camunda.bpm.engine.jobexecutor:187 - ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].
2025-09-02 08:06:36.794 [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] INFO  org.camunda.bpm.engine.jobexecutor:187 - ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs
2025-09-02 08:06:36.799 [main] INFO  org.jeecg.JeecgSystemApplication:53 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:9092/tld_ai/doc.html
	External: 	http://**************:9092/tld_ai/doc.html
	Swagger文档: 	http://**************:9092/tld_ai/doc.html
----------------------------------------------------------
2025-09-02 08:06:37.295 [RMI TCP Connection(2)-**************] INFO  o.a.c.c.C.[Tomcat].[localhost].[/tld_ai]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 08:06:37.297 [RMI TCP Connection(2)-**************] INFO  org.springframework.web.servlet.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-09-02 08:06:37.299 [RMI TCP Connection(2)-**************] INFO  org.springframework.web.servlet.DispatcherServlet:554 - Completed initialization in 2 ms
2025-09-02 08:06:37.549 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:749 - Starting Quartz Scheduler now, after delay of 1 seconds
2025-09-02 08:06:37.602 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3644 - ClusterManager: detected 1 failed or restarted instances.
2025-09-02 08:06:37.602 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3503 - ClusterManager: Scanning for instance "DESKTOP-IGMMD5L1756685919067"'s failed in-progress jobs.
2025-09-02 08:06:37.618 [Quartz Scheduler [MyScheduler]] INFO  org.quartz.core.QuartzScheduler:547 - Scheduler MyScheduler_$_DESKTOP-IGMMD5L1756771580211 started.
2025-09-02 08:23:07.661 [http-nio-9092-exec-1] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：12ms
2025-09-02 08:23:13.140 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-02 08:23:13.147 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-02 08:23:13.160 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:23:13.161 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-02 08:23:13.161 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-02 08:23:13.163 [http-nio-9092-exec-2] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-02 08:23:13.167 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-02 08:23:13.168 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-02 08:23:13.170 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:23:13.170 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-02 08:23:13.170 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-02 08:23:13.170 [http-nio-9092-exec-2] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-02 08:23:13.175 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-02 08:23:13.176 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-02 08:23:13.178 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:23:13.178 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-02 08:23:13.178 [http-nio-9092-exec-2] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-02 08:23:13.178 [http-nio-9092-exec-2] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-02 08:23:13.714 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-02 08:23:13.739 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)31毫秒
2025-09-02 08:23:13.744 [http-nio-9092-exec-4] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)36毫秒
2025-09-02 08:50:13.232 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysPermissionController:114 - ======获取全部菜单数据=====耗时:32毫秒
2025-09-02 08:55:28.291 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-02 08:55:28.292 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-02 08:55:28.295 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:55:28.295 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-02 08:55:28.295 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-02 08:55:28.295 [http-nio-9092-exec-5] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-02 08:55:28.299 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-02 08:55:28.300 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-02 08:55:28.301 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:55:28.302 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-02 08:55:28.302 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-02 08:55:28.302 [http-nio-9092-exec-5] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-02 08:55:28.306 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-02 08:55:28.307 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-02 08:55:28.309 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:55:28.310 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-02 08:55:28.310 [http-nio-9092-exec-5] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-02 08:55:28.310 [http-nio-9092-exec-5] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-02 08:55:28.538 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-02 08:55:28.544 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)6毫秒
2025-09-02 08:55:28.549 [http-nio-9092-exec-7] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)11毫秒
2025-09-02 08:56:11.484 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-02 08:56:11.485 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-02 08:56:11.488 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 08:56:11.488 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-02 08:56:11.488 [http-nio-9092-exec-10] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-02 08:56:11.489 [http-nio-9092-exec-10] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-02 08:56:11.663 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-02 08:56:11.669 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)6毫秒
2025-09-02 08:56:11.673 [http-nio-9092-exec-2] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)10毫秒
2025-09-02 09:45:35.077 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-02 09:45:35.079 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-02 09:45:35.081 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 09:45:35.082 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-02 09:45:35.082 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-02 09:45:35.082 [http-nio-9092-exec-7] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-02 09:45:35.086 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_flow where status = 'enable' 
2025-09-02 09:45:35.087 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_flow=SelectSqlInfo{fromTableName='airag_flow', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-02 09:45:35.089 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 09:45:35.090 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_flow
2025-09-02 09:45:35.090 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_flow"，查询字段 [name, id] 通过校验
2025-09-02 09:45:35.090 [http-nio-9092-exec-7] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_flow', alias='', fields=[name, id], all=false}] 
2025-09-02 09:45:35.095 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select name,id from airag_model where model_type = 'LLM' 
2025-09-02 09:45:35.096 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{airag_model=SelectSqlInfo{fromTableName='airag_model', fromSubSelect=null, aliasName='null', selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} 
2025-09-02 09:45:35.099 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 09:45:35.100 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: airag_model
2025-09-02 09:45:35.100 [http-nio-9092-exec-7] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"airag_model"，查询字段 [name, id] 通过校验
2025-09-02 09:45:35.100 [http-nio-9092-exec-7] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='airag_model', alias='', fields=[name, id], all=false}] 
2025-09-02 09:45:35.214 [http-nio-9092-exec-9] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-02 09:45:35.220 [http-nio-9092-exec-9] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)6毫秒
2025-09-02 09:45:35.227 [http-nio-9092-exec-9] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)13毫秒
2025-09-02 09:45:39.834 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-02 09:45:39.835 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-02 09:45:39.838 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 09:45:39.838 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-02 09:45:39.838 [http-nio-9092-exec-4] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-02 09:45:39.839 [http-nio-9092-exec-4] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-02 09:45:40.079 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-02 09:45:40.084 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)5毫秒
2025-09-02 09:45:40.090 [http-nio-9092-exec-6] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)11毫秒
2025-09-02 09:48:24.709 [http-nio-9092-exec-1] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:127 - 字典拼接的查询SQL：select realname,username from sys_user
2025-09-02 09:48:24.711 [http-nio-9092-exec-1] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:76 - 获取select sql信息 ：{sys_user=SelectSqlInfo{fromTableName='sys_user', fromSubSelect=null, aliasName='null', selectFields=[realname, username], realSelectFields=[realname, username], selectAll=false}} 
2025-09-02 09:48:24.714 [http-nio-9092-exec-1] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:59 - 表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, test_shoptype_tree=type_name,id, sys_dict=dict_code, airag_flow=name,id, oa_officialdoc_organcode=id,organ_name, demo=id,name, tj_user_report=name,username, sys_role=role_name,role_code, design_form=id,desform_name,desform_code, ces_shop_type=name,pid,id,has_child, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, sys_data_source=code,name, wu_liao=wul_name,id, sys_sms_template=template_code, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name, sys_tenant=name,id, airag_model=name,id}
2025-09-02 09:48:24.716 [http-nio-9092-exec-1] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:156 - checkWhiteList tableName: sys_user
2025-09-02 09:48:24.717 [http-nio-9092-exec-1] INFO  o.j.c.f.S.impl.DictTableWhiteListHandlerImpl:195 - 白名单校验：查询表"sys_user"，查询字段 [realname, username] 通过校验
2025-09-02 09:48:24.717 [http-nio-9092-exec-1] INFO  o.j.c.util.security.AbstractQueryBlackListHandler:66 -   获取sql信息 ：[QueryTable{name='sys_user', alias='', fields=[realname, username], all=false}] 
2025-09-02 09:48:24.848 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysAnnouncementController:348 - -----查询近两个月收到的未读通知-----，近2月的第一天：2025-08-01 00:00:00
2025-09-02 09:48:24.853 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysAnnouncementController:363 - begin 获取用户近2个月的系统公告 (通知)5毫秒
2025-09-02 09:48:24.859 [http-nio-9092-exec-3] INFO  o.j.m.system.controller.SysAnnouncementController:371 - end 获取用户2个月的系统公告 (系统消息)11毫秒
