<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined">新增角色首页配置</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                label: '编辑',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                label: '删除',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    
    <!-- 编辑弹窗 -->
    <RoleIndexModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import RoleIndexModal from './RoleIndexModal.vue';
import { getRoleIndexList, deleteRoleIndex } from '@/api/system/roleIndex';

const [registerModal, { openModal }] = useModal();

const columns = [
  {
    title: '角色编码',
    dataIndex: 'roleCode',
    width: 120,
  },
  {
    title: '首页URL',
    dataIndex: 'url',
    width: 200,
  },
  {
    title: '组件路径',
    dataIndex: 'component',
    width: 200,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return text === '1' ? '启用' : '禁用';
    },
  },
];

const [registerTable, { reload }] = useTable({
  title: '角色首页配置列表',
  api: getRoleIndexList,
  columns,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'roleCode',
        label: '角色编码',
        component: 'Input',
        colProps: { span: 8 },
      },
    ],
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
});

function handleAdd() {
  openModal(true, {
    isUpdate: false,
  });
}

function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
  });
}

async function handleDelete(record: Recordable) {
  await deleteRoleIndex(record.id);
  reload();
}

function handleSuccess() {
  reload();
}
</script>